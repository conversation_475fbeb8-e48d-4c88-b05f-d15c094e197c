// import type { InlineQueryContext } from 'grammy'
// import { Composer, InlineKeyboard, InlineQueryResultBuilder } from 'grammy'
// import type { Context } from '#root/bot/context.js'
// import { logHandle } from '#root/bot/helpers/logging.js'

// const composer = new Composer<Context>()

// // const feature = composer.chatType('private')

// // feature.inlineQuery(/best bot (framework|library)/, logHandle('inline-query'), async (ctx) => {
// // })
// composer.on('inline_query', logHandle('inline-query'), async (ctx: any) => {
//   const query = ctx.inlineQuery.query
//   // 创建一个单独的 inline query 结果。
//   const results = [
//     {
//       type: 'video',
//       id: '1', // 结果的唯一标识符
//       video_url: 'https://video.twimg.com/ext_tw_video/1726425621100724224/pu/vid/avc1/720x1280/UTxjPjHyPdiwNQ3E.mp4', // 视频文件的 URL
//       mime_type: 'video/mp4', // 视频的 MIME 类型
//       thumb_url: 'https://pbs.twimg.com/ext_tw_video_thumb/1726425621100724224/pu/img/4vYraC3ge3F8a1Pe.jpg', // 视频缩略图的 URL
//       title: '[Example Video] @Example Video \nsubtitle https://pbs.twimg.com/ext_tw_video_thumb/1726425621100724224/pu/img/4vYraC3ge3F8a1Pe.jpg', // 视频的标题
//       caption: 'This is an example video.', // 视频的说明文字
//       description: 'This is a description of the article. This is a description of the article.This is a description of the article.',
//       reply_markup: new InlineKeyboard().text('Click me', 'example_action'),
//       input_message_content: {
//         message_text: 'Click the button below to view the video.', // 占位符消息
//       },
//     },
//     {
//       type: 'video',
//       id: '2', // 结果的唯一标识符
//       video_url: 'https://video.twimg.com/ext_tw_video/1726425621100724224/pu/vid/avc1/720x1280/UTxjPjHyPdiwNQ3E.mp4', // 视频文件的 URL
//       mime_type: 'video/mp4', // 视频的 MIME 类型
//       thumb_url: 'https://pbs.twimg.com/ext_tw_video_thumb/1726425621100724224/pu/img/4vYraC3ge3F8a1Pe.jpg', // 视频缩略图的 URL
//       title: '[Example Video] @Example Video \nsubtitle https://pbs.twimg.com/ext_tw_video_thumb/1726425621100724224/pu/img/4vYraC3ge3F8a1Pe.jpg', // 视频的标题
//       caption: 'This is an example video.', // 视频的说明文字
//       reply_markup: new InlineKeyboard().text('Click me', 'example_action'),
//     },
//   ]

//   // 回复 inline query.
//   return await ctx.answerInlineQuery(
//     results, // 用结果列表回复
//     { cache_time: 1000 }, // 30 天的秒数
//   )
// })

// export { composer as inlineQueryFeature }
