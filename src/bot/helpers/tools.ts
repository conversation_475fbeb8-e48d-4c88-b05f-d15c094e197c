import { isBefore } from 'date-fns'

/**
 * 静默函数
 */
export function silentFn() {}

/**
 * 提取文本中的url
 * @param text 文本
 */
export function extractUrlFromText(text?: string) {
  if (!text)
    return null
  // 使用正则表达式提取第一个 URL
  const urlRegex = /https?:\/\/\S+/
  const match = text.match(urlRegex)
  if (match) {
    // 提取第一个匹配的 URL
    const url = match[0]
    // 使用 URL 对象解析 URL 并去掉查询参数
    const urlObject = new URL(url)
    urlObject.search = '' // 去掉查询参数
    return urlObject.href
  }

  // 如果没有找到 URL，返回 null
  return null
}

// 删除掉文本中最后一个链接
export function removeLastLink(text?: string) {
  if (!text)
    return text
  return text.replace(/\s*https?:\/\/\S+$/, '').trim()
}

/**
 * 验证会员是否有效
 * @param expiresAt
 */
export function isMembershipActive(expiresAt?: Date | null): boolean {
  // 如果 expiresAt 未定义则为无会员
  if (!expiresAt) {
    return false
  }
  // 获取当前日期和时间
  const now = new Date()

  // 检查当前日期是否在 expiresAt 之前
  return isBefore(now, expiresAt)
}

/**
 * 生成带随机小数的数值
 * @param {number} integerPart - 整数部分
 * @param {number} decimalPlaces - 小数位数（默认4位）
 * @returns {number} 生成的随机数
 */
export function generateRandomDecimal(integerPart: number, decimalPlaces = 4) {
  // 参数校验
  if (!Number.isInteger(integerPart)) {
    throw new TypeError('整数部分必须是整数')
  }

  // 生成随机小数部分
  let decimalStr = ''
  for (let i = 0; i < decimalPlaces; i++) {
    if (i === 0 || i === 1) {
      // 第一位随机最大位 5
      decimalStr += 0
    }
    else if (i === decimalPlaces - 1) {
      // 最后一位随机数不能位 0
      decimalStr += Math.floor(Math.random() * 3) + 1
    }
    else {
      decimalStr += Math.floor(Math.random() * 10)
    }
  }

  // 拼接并转换为数值类型
  return Number.parseFloat(`${integerPart}.${decimalStr}`)
}
