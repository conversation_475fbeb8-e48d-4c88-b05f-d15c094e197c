import type { TweetDetail } from '@prisma/client'
import { <PERSON><PERSON> } from 'hono'
import { createTweetDetail, getTweetDetailByTweetId, updateTweetDetailByTweetId } from '#root/db/tweet.js'
import { getTweetDetail, getTweetId, queryUser, queryUserTweets, validateTwitter } from '#root/bot/helpers/twitter.js'

const twitter = new Hono()
twitter.get('/detail', async (c) => {
  try {
    const url = c.req.query('url')
    if (!url)
      throw new Error('invalid tweet url')
    const { isTwitterUrl } = await validateTwitter(url)
    if (!isTwitterUrl) {
      throw new Error('invalid tweet url')
    }
    const tweetId = await getTweetId(url)
    let tweetDetail = await getTweetDetailByTweetId(tweetId)
    // 没有tweet记录先创建
    if (!tweetDetail) {
      const sourceTweet = await getTweetDetail(url)
      tweetDetail = await createTweetDetail({ tweetId, url, sourceTweet })
    }
    return c.json({
      success: true,
      data: tweetDetail.sourceTweet,
    })
  }
  catch (error: any) {
    return c.json({
      success: false,
      message: error?.message,
      error: error?.message,
    })
  }
})

twitter.post('/update-tweet-detail', async (c) => {
  const { tweetId, mediaGroups } = await c.req.json<TweetDetail>()
  try {
    const data = await updateTweetDetailByTweetId(tweetId, { mediaGroups })
    return c.json({
      success: true,
      data,
    })
  }
  catch (error: any) {
    return c.json({
      success: false,
      message: error?.message,
      error: error?.message,
    })
  }
})

twitter.get('/user-tweets', async (c) => {
  try {
    const url = c.req.query('url')
    if (!url)
      throw new Error('invalid tweet url')
    const { isTwitterUrl, screenName } = await validateTwitter(url)
    if (!isTwitterUrl) {
      throw new Error('invalid tweet url')
    }
    const data = await queryUser(screenName!)
    const userId = data.user_result_by_screen_name.result.rest_id
    const res = await queryUserTweets(userId)
    return c.json({
      success: true,
      data: res,
    })
  }
  catch (error: any) {
    return c.json({
      success: false,
      message: error?.message,
      error: error?.message,
    })
  }
})
export default twitter
