import { autoChatAction } from '@grammyjs/auto-chat-action'
import { hydrate } from '@grammyjs/hydrate'
import { hydrateReply } from '@grammyjs/parse-mode'
import type { BotConfig, StorageAdapter } from 'grammy'
import { Bot as TelegramBot } from 'grammy'
// import { sequentialize } from '@grammyjs/runner'
// import { ignoreOld } from 'grammy-middlewares'
import { hydrateFiles } from '@grammyjs/files'
import { conversations } from '@grammyjs/conversations'
import { welcomeFeature } from '#root/bot/features/welcome.js'
import { adminFeature } from '#root/bot/features/admin.js'
import { languageFeature } from '#root/bot/features/language.js'
import { unhandledFeature } from '#root/bot/features/unhandled.js'
import { errorHandler } from '#root/bot/handlers/error.js'
import { updateLogger } from '#root/bot/middlewares/update-logger.js'
import { session } from '#root/bot/middlewares/session.js'
import type { Context, SessionData } from '#root/bot/context.js'
import { createContextConstructor } from '#root/bot/context.js'
import { i18n, isMultipleLocales } from '#root/bot/i18n.js'
import type { Logger } from '#root/logger.js'
import type { Config } from '#root/config.js'
import { authMiddleware } from '#root/bot/middlewares/auth-middleware.js'
// import { inlineQueryFeature } from '#root/bot/features/inline-query.js'
import { downloadFeature } from '#root/bot/features/download.js'
import { purchaseFeature } from '#root/bot/features/purchase.js'
import { dailyrollFeature } from '#root/bot/features/daily-roll.js'

interface Dependencies {
  config: Config
  logger: Logger
}

interface Options {
  botSessionStorage?: StorageAdapter<SessionData>
  botConfig?: Omit<BotConfig<Context>, 'ContextConstructor'>
}

function getSessionKey(ctx: Omit<Context, 'session'>) {
  // 有 callbackQuery/ inlineQuery 的时候，chat 是 undefined
  return ctx?.chat?.id?.toString() ?? ctx?.inlineQuery?.from?.id?.toString() ?? ctx?.callbackQuery?.from?.id?.toString()
}

export function createBot(token: string, dependencies: Dependencies, options: Options = {}) {
  const {
    config,
    logger,
  } = dependencies

  const bot = new TelegramBot(token, {
    ...options.botConfig,
    client: { apiRoot: 'http://************:8081' },
    ContextConstructor: createContextConstructor({
      logger,
      config,
    }),
  })
  const protectedBot = bot.errorBoundary(errorHandler)

  // Middlewares
  // bot.api.config.use(parseMode('HTML'))
  bot.api.config.use(hydrateFiles(bot.token))

  // todo 根据语言定义命令
  bot.api.setMyCommands([
    { command: 'start', description: '🤖 Start the bot' },
    { command: 'lang', description: '🌍 Set bot language' },
    { command: 'purchase', description: '🎁 Buy Membership' },
    { command: 'my', description: '👤 View Profile' },
    { command: 'dailyroll', description: '🎲 Daily roll 5 per day' },
  ])
  // if (config.isPollingMode)
  //   protectedBot.use(sequentialize(getSessionKey))
  if (config.isDebug)
    protectedBot.use(updateLogger())
  // 暂时用不上
  // protectedBot.use(ignoreOld())
  protectedBot.use(autoChatAction(bot.api))
  protectedBot.use(hydrateReply)
  protectedBot.use(hydrate())
  protectedBot.use(session({ getSessionKey, storage: options.botSessionStorage }))
  protectedBot.use(i18n)
  protectedBot.use(authMiddleware())
  if (isMultipleLocales)
    protectedBot.use(languageFeature)
  protectedBot.use(conversations())

  // Handlers
  // protectedBot.use(inlineQueryFeature)
  protectedBot.use(adminFeature)
  protectedBot.use(welcomeFeature)
  protectedBot.use(purchaseFeature)
  protectedBot.use(dailyrollFeature)
  protectedBot.use(downloadFeature)

  // must be the last handler
  protectedBot.use(unhandledFeature)

  return bot
}

export type Bot = ReturnType<typeof createBot>
