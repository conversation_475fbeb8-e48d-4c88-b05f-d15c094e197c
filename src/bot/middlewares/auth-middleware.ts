import type { Middleware } from 'grammy'
import { isSameDay } from 'date-fns'
import type { Context } from '#root/bot/context.js'
import { getMembershipByTelegramId, upsertUser } from '#root/db/user.js'

// 登录中间件
export function authMiddleware(): Middleware<Context> {
  return async (ctx, next) => {
    // todo 群组 下载等
    if (!ctx.from || ctx.chat?.type !== 'private')
      return next()
    if (!ctx.session.updateAt) {
      ctx.session.updateAt = new Date()
    }
    const currentDate = new Date()
    const isSameDayCheck = isSameDay(currentDate, ctx.session.updateAt)
    // 检查是否已缓存用户
    if (!ctx.session.user) {
      const payload = {
        telegramId: String(ctx.from?.id),
        ...ctx.from,
      } as any
      const user = await upsertUser(payload)
      ctx.session.user = user
      if (user.current_language) {
        ctx.session.__language_code = user.current_language
        await ctx.i18n.setLocale(user.current_language)
      }
    }
    // 超过第二天自动更新一次 并刷新时间
    if (!ctx.session.membership || !isSameDayCheck) {
      const membership = await getMembershipByTelegramId(String(ctx.from.id))
      ctx.session.membership = membership
      ctx.session.updateAt = new Date()
    }
    return next()
  }
}
