import { chatAction } from '@grammyjs/auto-chat-action'
import { Composer } from 'grammy'
import { createConversation } from '@grammyjs/conversations'
import type { Context } from '#root/bot/context.js'
import { isAdmin } from '#root/bot/filters/is-admin.js'
import { setCommandsHandler } from '#root/bot/handlers/commands/setcommands.js'
import { logHandle } from '#root/bot/helpers/logging.js'
import { sendBroadcast } from '#root/bot/conversions/boardcast.js'
import { adsData } from '#root/bot/callback-data/ads.js'
import { broadcastMessageToAllUsers } from '#root/redis/boardcast.js'

const composer = new Composer<Context>()

composer.use(createConversation(sendBroadcast as any, 'send-broadcast'))

const feature = composer.chatType('private').filter((ctx) => {
  return isAdmin(ctx.config.botAdmins)(ctx)
})

feature.command(
  'setcommands',
  logHandle('command-setcommands'),
  chatAction('typing'),
  setCommandsHandler,
)

feature.command(
  'sendads',
  logHandle('command-sendBroadcast'),
  chatAction('typing'),
  async (ctx) => {
    return await ctx.conversation.enter('send-broadcast')
  },
)

feature.callbackQuery(
  adsData.filter(),
  logHandle('keyboard-send-ads'),
  async (ctx) => {
    const { chatId, msgId } = adsData.unpack(
      ctx.callbackQuery.data,
    )
    try {
      const res = await broadcastMessageToAllUsers(chatId, msgId)
      await ctx.editMessageText(res.message)
    }
    catch (error: any) {
      await ctx.editMessageText(error.message)
    }
  },
)

export { composer as adminFeature }
