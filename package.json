{"name": "x-download-bot", "type": "module", "version": "0.1.0", "private": true, "description": "x download bot", "imports": {"#root/*": "./build/src/*"}, "license": "MIT", "engines": {"node": ">=20.0.0", "npm": ">=8.0.0"}, "scripts": {"lint": "eslint .", "format": "eslint . --fix", "typecheck": "tsc", "build": "tsc --noEmit false", "dev": "cross-env NODE_ENV=development tsc-watch --onSuccess \"tsx ./src/main.ts\"", "start": "tsc && tsx ./src/main.ts", "start:force": "tsx ./src/main.ts", "db": "npx prisma generate && npx prisma db push", "prepare": "husky || true"}, "dependencies": {"@ai-sdk/openai": "^1.1.0", "@grammyjs/auto-chat-action": "0.1.1", "@grammyjs/conversations": "^2.1.0", "@grammyjs/emoji": "^1.2.0", "@grammyjs/files": "^1.1.1", "@grammyjs/hydrate": "1.4.1", "@grammyjs/i18n": "1.1.2", "@grammyjs/menu": "^1.3.0", "@grammyjs/parse-mode": "1.10.0", "@grammyjs/ratelimiter": "^1.2.1", "@grammyjs/runner": "2.0.3", "@grammyjs/storage-prisma": "^2.4.2", "@grammyjs/types": "3.11.1", "@hono/node-server": "1.13.5", "@lemonsqueezy/lemonsqueezy.js": "^4.0.0", "@prisma/client": "^6.2.1", "@remotion/media-parser": "^4.0.252", "@roziscoding/grammy-autoquote": "^2.0.8", "@types/node-cron": "^3.0.11", "agent-twitter-client": "^0.0.18", "ai": "^4.1.0", "bullmq": "^5.40.2", "callback-data": "1.1.1", "cross-env": "^7.0.3", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "got": "^14.4.5", "grammy": "1.27.0", "grammy-guard": "0.5.0", "grammy-middlewares": "^1.0.11", "hono": "4.6.5", "ioredis": "^5.5.0", "lodash-es": "^4.17.21", "node-cron": "^3.0.3", "node-fetch": "^3.3.2", "pino": "9.4.0", "pino-pretty": "11.2.2", "playwright": "^1.52.0", "tsx": "4.19.1", "twitter-api-v2": "^1.22.0", "twitter-openapi-typescript": "^0.0.53", "twitter-openapi-typescript-generated": "^0.0.38", "valibot": "0.42.1"}, "devDependencies": {"@antfu/eslint-config": "2.26.0", "@types/fluent-ffmpeg": "^2.1.27", "@types/lodash-es": "^4.17.12", "@types/node": "^20.14.12", "eslint": "^9.10.0", "husky": "^9.1.6", "lint-staged": "^15.2.10", "prisma": "^6.2.1", "source-map-support": "^0.5.21", "tsc-watch": "^6.2.0", "typescript": "^5.6.3"}, "lint-staged": {"*.ts": "eslint"}}