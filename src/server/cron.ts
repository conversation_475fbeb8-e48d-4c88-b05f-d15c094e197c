import cron from 'node-cron'
import type { ScheduledTask } from 'node-cron'
import { updateMembershipsCredit } from '#root/db/user.js'
import { deleteExpiredUserTweets } from '#root/db/tweet.js'
import { checkAndUpdateExpiredRecharges } from '#root/db/recharge.js'
import { checkAndProcessTRC20Deposits } from '#root/db/tron.js'

class CronManager {
  private tasks: Map<string, ScheduledTask>

  constructor() {
    this.tasks = new Map()

    // 注册所有定时任务
    this.register('daily', '0 0 * * *', async () => {
      await updateMembershipsCredit()
    })

    this.register('minutely', '* * * * *', async () => {
      try {
        await deleteExpiredUserTweets()
        // 检测过期充值订单
        await checkAndUpdateExpiredRecharges()
      }
      catch (error) {
        console.error('清理过期 UserTweets 失败:', error)
      }
    })

    // - 检测TRC20充值记录
    this.register(
      'checkTRC20Deposits',
      '*/40 * * * * *', // 40 秒执行一次
      async () => {
        await checkAndProcessTRC20Deposits()
      },
    )
  }

  private register(name: string, cronExpression: string, handler: () => Promise<void>) {
    const task = cron.schedule(cronExpression, handler, {
      scheduled: false,
      timezone: 'Asia/Shanghai',
    })
    this.tasks.set(name, task)
  }

  start() {
    for (const task of this.tasks.values()) {
      task.start()
    }
  }

  stop() {
    for (const task of this.tasks.values()) {
      task.stop()
    }
  }
}

export const cronManager = new CronManager()
