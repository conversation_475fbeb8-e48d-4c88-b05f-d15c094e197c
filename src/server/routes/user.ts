import { Hono } from 'hono'
import { getMembershipByTelegramId, updateMembershipQuota } from '#root/db/user.js'

const user = new Hono()

user.get('/:telegramId', async (c) => {
  const telegramId = c.req.param('telegramId')
  try {
    const data = await getMembershipByTelegramId(String(telegramId))
    return c.json({
      success: true,
      data,
    })
  }
  catch (error: any) {
    return c.json({
      success: false,
      message: error?.message,
      error: error?.message,
    })
  }
})

user.post('/update-member-quota', async (c) => {
  const { telegramId, amount } = await c.req.json<{
    telegramId: string
    amount: number
  }>()

  try {
    const data = await updateMembershipQuota(String(telegramId), Number(amount))
    return c.json({
      success: true,
      data,
    })
  }
  catch (error: any) {
    return c.json({
      success: false,
      message: error?.message,
      error: error?.message,
    })
  }
})
export default user
