import { Composer } from 'grammy'
import type { Context } from '#root/bot/context.js'
import { logHandle } from '#root/bot/helpers/logging.js'
import { createChangeLanguageKeyboard } from '#root/bot/keyboards/change-language.js'

const composer = new Composer<Context>()

const feature = composer.chatType('private')

feature.command('start', logHandle('command-start'), async (ctx) => {
  await ctx.reply(ctx.t('welcome'))
  if (!ctx.session.__language_code) {
    await ctx.reply(ctx.t('language-select'), {
      reply_markup: await createChangeLanguageKeyboard(ctx),
    })
  }
})

export { composer as welcomeFeature }
