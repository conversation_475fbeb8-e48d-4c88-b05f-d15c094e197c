import { Redis } from 'ioredis'
import { config } from '#root/config.js'
import { logger } from '#root/logger.js'

// Redis 连接配置
export const redisConfig = {
  host: '************',
  // host: 'localhost',
  // host: 'fit-buck-55291.upstash.io',
  // username: 'default',
  // password: 'Adf7AAIjcDE5NTczNTRjZjAyYTc0YmYxYjZkNWVhYzI5MDI0MDJjOHAxMA',
  port: config.redisPort,
  maxRetriesPerRequest: null,
  enableAutoPipelining: true,
}

export const redis = new Redis(redisConfig)

// 只在主连接上监听事件，避免重复日志
let isMainConnectionLogged = false
redis.on('connect', () => {
  if (!isMainConnectionLogged) {
    logger.info(`🚀 Redis 已连接: ${redis.options.host}:${redis.options.port}`)
    isMainConnectionLogged = true
  }
})
