import { InlineKeyboard } from 'grammy'
import { chunk } from 'lodash-es'
import { changeLanguageData } from '#root/bot/callback-data/change-language.js'
import type { Context } from '#root/bot/context.js'
import { i18n } from '#root/bot/i18n.js'

export const LanguageCodes = {
  'zh-hans': '🇨🇳 简体中文',
  'zh-hant': '🇭🇰 繁體中文',
  'en': '🇺🇸 English',
  'ar': '🇸🇦 العربية',
  'fa': '🇮🇷 فارسی',
  'id': '🇮🇩 Bahasa Indonesia',
  // 'ru': '🇷🇺 Русский',
  // 'ja': '🇯🇵 日本語',
  // 'ko': '🇰🇷 한국어',
  // 'fr': '🇫🇷 Français',
  // 'de': '🇩🇪 Deutsch',
  // 'it': '🇮🇹 Italiano',
  // 'nl': '🇳🇱 Nederlands',
  // 'el': '🇬🇷 Ελληνικά',
  // 'pt': '🇵🇹 Português',
  // 'sv': '🇸🇪 Svenska',
  // 'da': '🇩🇰 Dansk',
  // 'fi': '🇫🇮 Suomi',
  // 'hi': '🇮🇳 हिन्दी',
  // 'ur': '🇵🇰 اردو',
  // 'tl': '🇵🇭 Tagalog (Filipino)',
  // 'ms': '🇲🇾 Bahasa Melayu',
  // 'ta': '🇮🇳 தமிழ்',
  // 'km': '🇰🇭 ខ្មែរ',
  // 'lo': '🇱🇦 ລາວ',
  // 'my': '🇲🇲 မြန်မာ',
  // 'he': '🇮🇱 עברית',
} as const
export type LanguageCode = keyof typeof LanguageCodes

export function getLangName(code: LanguageCode) {
  return LanguageCodes[code]
}

/** 设置面板语言 */
export async function createChangeLanguageKeyboard(ctx: Context) {
  // const currentLocaleCode = await ctx.i18n.getLocale()
  const currentLocaleCode = ctx.session.__language_code
  const getLabel = (code: LanguageCode) => {
    const isActive = code === currentLocaleCode

    return `${isActive ? '✅ ' : ''}${getLangName(code)}`
  }

  return InlineKeyboard.from(
    chunk(
      i18n.locales.map(localeCode => ({
        text: getLabel(localeCode as LanguageCode),
        callback_data: changeLanguageData.pack({
          code: localeCode,
        }),
      })),
      2,
    ),
  )
}
