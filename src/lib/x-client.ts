import type { TweetApiUtilsData, TwitterOpenApiClient } from 'twitter-openapi-typescript'
import { TwitterOpenApi } from 'twitter-openapi-typescript'
import { find, pick } from 'lodash-es'
import { logger } from '#root/logger.js'

const cookieList = [
  // // soa
  // [
  //   {
  //     domain: '.x.com',
  //     expirationDate: 1787338222.570208,
  //     hostOnly: false,
  //     httpOnly: true,
  //     name: 'auth_token',
  //     path: '/',
  //     sameSite: 'no_restriction',
  //     secure: true,
  //     session: false,
  //     storeId: null,
  //     value: '28d6cc4fc1716de404b91eaf58df1b4e92ad9c3d',
  //   },
  //   {
  //     domain: '.x.com',
  //     expirationDate: 1752787175.919906,
  //     hostOnly: false,
  //     httpOnly: false,
  //     name: 'gt',
  //     path: '/',
  //     sameSite: null,
  //     secure: true,
  //     session: false,
  //     storeId: null,
  //     value: '1945918823854682530',
  //   },
  //   {
  //     domain: '.x.com',
  //     expirationDate: 1787338175.795271,
  //     hostOnly: false,
  //     httpOnly: false,
  //     name: 'guest_id',
  //     path: '/',
  //     sameSite: 'no_restriction',
  //     secure: true,
  //     session: false,
  //     storeId: null,
  //     value: 'v1%3A175277817566819729',
  //   },
  //   {
  //     domain: '.x.com',
  //     expirationDate: 1784314224.568174,
  //     hostOnly: false,
  //     httpOnly: false,
  //     name: 'twid',
  //     path: '/',
  //     sameSite: 'no_restriction',
  //     secure: true,
  //     session: false,
  //     storeId: null,
  //     value: 'u%3D168574344',
  //   },
  //   {
  //     domain: '.x.com',
  //     expirationDate: 1752779975.795292,
  //     hostOnly: false,
  //     httpOnly: true,
  //     name: '__cf_bm',
  //     path: '/',
  //     sameSite: 'no_restriction',
  //     secure: true,
  //     session: false,
  //     storeId: null,
  //     value: 'tRUJ8Ul_SN3Q9BhQVjM5AtrKZ6DjthsUWVG.fJqW3iE-1752778175-*******-eZItRttFpaPopnix3gK0gLa0zfL3XkTbd8xsvzz36CKcMrEqsIcfa64EXSkumfaNS3rcpkgxhx2g8HJP5YlPBdesZxOrhi8onhtPWpTmhZQ',
  //   },
  //   {
  //     domain: '.x.com',
  //     expirationDate: 1752864623.333377,
  //     hostOnly: false,
  //     httpOnly: true,
  //     name: 'att',
  //     path: '/',
  //     sameSite: 'no_restriction',
  //     secure: true,
  //     session: false,
  //     storeId: null,
  //     value: '1-JVzqasi5amUKEoFqpV4p2OVEyMcvDjAT5Dw9XUm1',
  //   },
  //   {
  //     domain: '.x.com',
  //     expirationDate: 1787338223.006142,
  //     hostOnly: false,
  //     httpOnly: false,
  //     name: 'ct0',
  //     path: '/',
  //     sameSite: 'lax',
  //     secure: true,
  //     session: false,
  //     storeId: null,
  //     value: '3ea620035648995fc812c0635580d828a7e5f5962f66127818d3c94dadeb98096ce6f0c4f7eb1a3aad8bcda75d3920663a69d1046fcbeb13b1c58ee17327541d832ea34b38d90170d203b90ade661b02',
  //   },
  //   {
  //     domain: '.x.com',
  //     expirationDate: 1787338224.568119,
  //     hostOnly: false,
  //     httpOnly: false,
  //     name: 'guest_id_ads',
  //     path: '/',
  //     sameSite: 'no_restriction',
  //     secure: true,
  //     session: false,
  //     storeId: null,
  //     value: 'v1%3A175277817566819729',
  //   },
  //   {
  //     domain: '.x.com',
  //     expirationDate: 1787338224.568158,
  //     hostOnly: false,
  //     httpOnly: false,
  //     name: 'guest_id_marketing',
  //     path: '/',
  //     sameSite: 'no_restriction',
  //     secure: true,
  //     session: false,
  //     storeId: null,
  //     value: 'v1%3A175277817566819729',
  //   },
  //   {
  //     domain: '.x.com',
  //     expirationDate: 1787338222.569952,
  //     hostOnly: false,
  //     httpOnly: true,
  //     name: 'kdt',
  //     path: '/',
  //     sameSite: null,
  //     secure: true,
  //     session: false,
  //     storeId: null,
  //     value: 'o2ynYqZWvl7LiCRZSAQI7HlgpW1ShfZXdMv8yt3r',
  //   },
  //   {
  //     domain: '.x.com',
  //     expirationDate: 1787338175.919706,
  //     hostOnly: false,
  //     httpOnly: false,
  //     name: 'personalization_id',
  //     path: '/',
  //     sameSite: 'no_restriction',
  //     secure: true,
  //     session: false,
  //     storeId: null,
  //     value: '"v1_RfSFByDYvB8+9Nw/di1uWg=="',
  //   },
  // ],
  // // fin
  // [
  //   {
  //     domain: '.x.com',
  //     expirationDate: 1786465523.417498,
  //     hostOnly: false,
  //     httpOnly: true,
  //     name: 'auth_token',
  //     path: '/',
  //     sameSite: 'no_restriction',
  //     secure: true,
  //     session: false,
  //     storeId: null,
  //     value: '0ebb7fbf890dd14727b8a1efec76018ba8917c62',
  //   },
  //   {
  //     domain: '.x.com',
  //     expirationDate: 1786465508.208717,
  //     hostOnly: false,
  //     httpOnly: false,
  //     name: 'guest_id',
  //     path: '/',
  //     sameSite: 'no_restriction',
  //     secure: true,
  //     session: false,
  //     storeId: null,
  //     value: 'v1%3A175190550828297838',
  //   },
  //   {
  //     domain: '.x.com',
  //     expirationDate: 1784314361.265191,
  //     hostOnly: false,
  //     httpOnly: false,
  //     name: 'twid',
  //     path: '/',
  //     sameSite: 'no_restriction',
  //     secure: true,
  //     session: false,
  //     storeId: null,
  //     value: 'u%3D1732950713716948992',
  //   },
  //   {
  //     domain: '.x.com',
  //     hostOnly: false,
  //     httpOnly: true,
  //     name: '_twitter_sess',
  //     path: '/',
  //     sameSite: null,
  //     secure: true,
  //     session: true,
  //     storeId: null,
  //     value: 'BAh7BiIKZmxhc2hJQzonQWN0aW9uQ29udHJvbGxlcjo6Rmxhc2g6OkZsYXNo%250ASGFzaHsABjoKQHVzZWR7AA%253D%253D--1164b91ac812d853b877e93ddb612b7471bebc74',
  //   },
  //   {
  //     domain: '.x.com',
  //     expirationDate: 1753355649.913655,
  //     hostOnly: false,
  //     httpOnly: false,
  //     name: 'external_referer',
  //     path: '/',
  //     sameSite: null,
  //     secure: true,
  //     session: false,
  //     storeId: null,
  //     value: 'padhuUp37zjKE2wiJkYN0NRYfw9W8LjYtlMH7DtAmxU%3D|0|8e8t2xd8A2w%3D',
  //   },
  //   {
  //     domain: '.x.com',
  //     expirationDate: 1752780154.314092,
  //     hostOnly: false,
  //     httpOnly: true,
  //     name: '__cf_bm',
  //     path: '/',
  //     sameSite: 'no_restriction',
  //     secure: true,
  //     session: false,
  //     storeId: null,
  //     value: '_9wiMpSJkazhw7m8.VDrdfosY9O6Px7EXpI4WjFGU7M-1752778354-*******-HItu_oT4zmQw3ZVRymg9Dyohgq2Qnzed8d0hCXS4Fl2pRp2.CJGm6Kb_ZWQGH1sbJTDuqzAzKbfvGmwgDfx7eLuqmiSEPu_fe_NHoRrDAoY',
  //   },
  //   {
  //     domain: '.x.com',
  //     expirationDate: 1786465524.118839,
  //     hostOnly: false,
  //     httpOnly: false,
  //     name: 'ct0',
  //     path: '/',
  //     sameSite: 'lax',
  //     secure: true,
  //     session: false,
  //     storeId: null,
  //     value: 'b226aa8ebc241ce76f54cd9bae0776b29375399122fbff8d4e3306091176657803e68a59bea40913a6a73a9e5584f85283af0d4e7009684671dfd55835c6181cff15851cf2e3a3ab140f0a3468818234',
  //   },
  //   {
  //     domain: '.x.com',
  //     expirationDate: 1767780446.459618,
  //     hostOnly: false,
  //     httpOnly: false,
  //     name: 'd_prefs',
  //     path: '/',
  //     sameSite: null,
  //     secure: true,
  //     session: false,
  //     storeId: null,
  //     value: 'MToxLGNvbnNlbnRfdmVyc2lvbjoyLHRleHRfdmVyc2lvbjoxMDAw',
  //   },
  //   {
  //     domain: '.x.com',
  //     expirationDate: 1787338361.265139,
  //     hostOnly: false,
  //     httpOnly: false,
  //     name: 'guest_id_ads',
  //     path: '/',
  //     sameSite: 'no_restriction',
  //     secure: true,
  //     session: false,
  //     storeId: null,
  //     value: 'v1%3A175190550828297838',
  //   },
  //   {
  //     domain: '.x.com',
  //     expirationDate: 1787338361.265174,
  //     hostOnly: false,
  //     httpOnly: false,
  //     name: 'guest_id_marketing',
  //     path: '/',
  //     sameSite: 'no_restriction',
  //     secure: true,
  //     session: false,
  //     storeId: null,
  //     value: 'v1%3A175190550828297838',
  //   },
  //   {
  //     domain: '.x.com',
  //     expirationDate: 1786465523.417328,
  //     hostOnly: false,
  //     httpOnly: true,
  //     name: 'kdt',
  //     path: '/',
  //     sameSite: null,
  //     secure: true,
  //     session: false,
  //     storeId: null,
  //     value: 'n4iwVUYKQqc79rsRwsH5I5D49eZlJIOEXNpdLunW',
  //   },
  //   {
  //     domain: '.x.com',
  //     expirationDate: 1786442848.477498,
  //     hostOnly: false,
  //     httpOnly: false,
  //     name: 'personalization_id',
  //     path: '/',
  //     sameSite: 'no_restriction',
  //     secure: true,
  //     session: false,
  //     storeId: null,
  //     value: '"v1_L/p905yLwqyLP0PdvvC+tQ=="',
  //   },
  // ],
  // // chatAi
  // [
  //   {
  //     domain: '.x.com',
  //     expirationDate: 1787338468.164999,
  //     hostOnly: false,
  //     httpOnly: true,
  //     name: 'auth_token',
  //     path: '/',
  //     sameSite: 'no_restriction',
  //     secure: true,
  //     session: false,
  //     storeId: null,
  //     value: '2a4afae3d10ad2c68cf5213821d10bfa5d5e0497',
  //   },
  //   {
  //     domain: '.x.com',
  //     expirationDate: 1787338468.436349,
  //     hostOnly: false,
  //     httpOnly: false,
  //     name: 'guest_id',
  //     path: '/',
  //     sameSite: 'no_restriction',
  //     secure: true,
  //     session: false,
  //     storeId: null,
  //     value: 'v1%3A175277846829019435',
  //   },
  //   {
  //     domain: '.x.com',
  //     expirationDate: 1787338468.164822,
  //     hostOnly: false,
  //     httpOnly: false,
  //     name: 'ads_prefs',
  //     path: '/',
  //     sameSite: 'no_restriction',
  //     secure: true,
  //     session: false,
  //     storeId: null,
  //     value: '"HBISAAA="',
  //   },
  //   {
  //     domain: '.x.com',
  //     expirationDate: 1784314477.645575,
  //     hostOnly: false,
  //     httpOnly: false,
  //     name: 'twid',
  //     path: '/',
  //     sameSite: 'no_restriction',
  //     secure: true,
  //     session: false,
  //     storeId: null,
  //     value: 'u%3D1801234315579179008',
  //   },
  //   {
  //     domain: '.x.com',
  //     expirationDate: 1787338469.260772,
  //     hostOnly: false,
  //     httpOnly: true,
  //     name: 'auth_multi',
  //     path: '/',
  //     sameSite: 'lax',
  //     secure: true,
  //     session: false,
  //     storeId: null,
  //     value: '"1700415026576891904:9aca926e8744692fd32fe861103d765ee15cfb92"',
  //   },
  //   {
  //     domain: '.x.com',
  //     expirationDate: 1752780264.96822,
  //     hostOnly: false,
  //     httpOnly: true,
  //     name: '__cf_bm',
  //     path: '/',
  //     sameSite: 'no_restriction',
  //     secure: true,
  //     session: false,
  //     storeId: null,
  //     value: '7pmvPaZBJokyORAOA7eyRlHLBOabKTYHWHOcsR0oyek-1752778464-*******-g.jhZFXulAceczPoXRPe1OXmu6KzyAHHQM1hbADExFNZ34YO.P8Q4EocBw9uB9ZWA6dSzSrmkCBc1R_xN_FYRBgsGIZ_4n8_utFktFj1CTM',
  //   },
  //   {
  //     domain: '.x.com',
  //     expirationDate: 1787338468.436446,
  //     hostOnly: false,
  //     httpOnly: false,
  //     name: 'ct0',
  //     path: '/',
  //     sameSite: 'lax',
  //     secure: true,
  //     session: false,
  //     storeId: null,
  //     value: 'ce83bd4c7a4ba2b28b318a04b460be2ce502bb5826032e4df515632f8bd6427ee9fc7e225556d3ebad9c9369eaed085f5a631835c724105ec2c2415035a908452d0982be1b918b71557315cbb539fdf9',
  //   },
  //   {
  //     domain: '.x.com',
  //     expirationDate: 1767781481.54511,
  //     hostOnly: false,
  //     httpOnly: false,
  //     name: 'd_prefs',
  //     path: '/',
  //     sameSite: null,
  //     secure: true,
  //     session: false,
  //     storeId: null,
  //     value: 'MToxLGNvbnNlbnRfdmVyc2lvbjoyLHRleHRfdmVyc2lvbjoxMDAw',
  //   },
  //   {
  //     domain: '.x.com',
  //     expirationDate: 1787338468.164764,
  //     hostOnly: false,
  //     httpOnly: false,
  //     name: 'dnt',
  //     path: '/',
  //     sameSite: 'no_restriction',
  //     secure: true,
  //     session: false,
  //     storeId: null,
  //     value: '1',
  //   },
  //   {
  //     domain: '.x.com',
  //     expirationDate: 1787338477.645499,
  //     hostOnly: false,
  //     httpOnly: false,
  //     name: 'guest_id_ads',
  //     path: '/',
  //     sameSite: 'no_restriction',
  //     secure: true,
  //     session: false,
  //     storeId: null,
  //     value: 'v1%3A175277846829019435',
  //   },
  //   {
  //     domain: '.x.com',
  //     expirationDate: 1787338477.645549,
  //     hostOnly: false,
  //     httpOnly: false,
  //     name: 'guest_id_marketing',
  //     path: '/',
  //     sameSite: 'no_restriction',
  //     secure: true,
  //     session: false,
  //     storeId: null,
  //     value: 'v1%3A175277846829019435',
  //   },
  //   {
  //     domain: '.x.com',
  //     expirationDate: 1786616548.605635,
  //     hostOnly: false,
  //     httpOnly: true,
  //     name: 'kdt',
  //     path: '/',
  //     sameSite: null,
  //     secure: true,
  //     session: false,
  //     storeId: null,
  //     value: 'wmCaLlk5x63Nn2JbyL5v3XNYylwjG0zgegOojDXA',
  //   },
  //   {
  //     domain: '.x.com',
  //     expirationDate: 1786443882.869493,
  //     hostOnly: false,
  //     httpOnly: false,
  //     name: 'personalization_id',
  //     path: '/',
  //     sameSite: 'no_restriction',
  //     secure: true,
  //     session: false,
  //     storeId: null,
  //     value: '"v1_TREijUWuYXK3l1FdTxR4wg=="',
  //   },
  // ],

  // downloadx
  [
    {
      domain: '.x.com',
      expirationDate: 1788535069.493013,
      hostOnly: false,
      httpOnly: true,
      name: 'auth_token',
      path: '/',
      sameSite: 'no_restriction',
      secure: true,
      session: false,
      storeId: null,
      value: '9aca926e8744692fd32fe861103d765ee15cfb92',
    },
    {
      domain: '.x.com',
      expirationDate: 1788535069.901466,
      hostOnly: false,
      httpOnly: false,
      name: 'guest_id',
      path: '/',
      sameSite: 'no_restriction',
      secure: true,
      session: false,
      storeId: null,
      value: 'v1%3A175397506971555740',
    },
    {
      domain: '.x.com',
      expirationDate: 1788535069.492759,
      hostOnly: false,
      httpOnly: false,
      name: 'ads_prefs',
      path: '/',
      sameSite: 'no_restriction',
      secure: true,
      session: false,
      storeId: null,
      value: '"HBESAAA="',
    },
    {
      domain: '.x.com',
      expirationDate: 1785598242.432896,
      hostOnly: false,
      httpOnly: false,
      name: 'twid',
      path: '/',
      sameSite: 'no_restriction',
      secure: true,
      session: false,
      storeId: null,
      value: 'u%3D1700415026576891904',
    },
    {
      domain: '.x.com',
      expirationDate: 1788622237.72653,
      hostOnly: false,
      httpOnly: true,
      name: 'auth_multi',
      path: '/',
      sameSite: 'lax',
      secure: true,
      session: false,
      storeId: null,
      value: '"1801234315579179008:2a4afae3d10ad2c68cf5213821d10bfa5d5e0497"',
    },
    {
      domain: '.x.com',
      expirationDate: 1754064036.531072,
      hostOnly: false,
      httpOnly: true,
      name: '__cf_bm',
      path: '/',
      sameSite: 'no_restriction',
      secure: true,
      session: false,
      storeId: null,
      value: 'ASRVdii0hyZofWNPLt4y32GrhvjDMMh81Scdtc4qrb8-1754062236-*******-Pl42zWA7enPLuZzeYfUuufZXbH7rZSKj1IWLUJQ5lgYLxwp4XeChSUFtSHRIDRrNI0pSP3EO2XFnQesQNpWXelceEjsETlOvLPE1hpJDCXY',
    },
    {
      domain: '.x.com',
      expirationDate: 1788535069.9015,
      hostOnly: false,
      httpOnly: false,
      name: 'ct0',
      path: '/',
      sameSite: 'lax',
      secure: true,
      session: false,
      storeId: null,
      value: 'a1fafeea2302660d82ad2771dfb658d9915822aee8bd97655f31a096d269c9288c1adf79a8b8588c3775be7e10420f9275f59df715e6365663b093ec517b1f7a0a055574105897bfb88f946d45c6047b',
    },
    {
      domain: '.x.com',
      expirationDate: 1767781481.54511,
      hostOnly: false,
      httpOnly: false,
      name: 'd_prefs',
      path: '/',
      sameSite: null,
      secure: true,
      session: false,
      storeId: null,
      value: 'MToxLGNvbnNlbnRfdmVyc2lvbjoyLHRleHRfdmVyc2lvbjoxMDAw',
    },
    {
      domain: '.x.com',
      expirationDate: 1788535069.492705,
      hostOnly: false,
      httpOnly: false,
      name: 'dnt',
      path: '/',
      sameSite: 'no_restriction',
      secure: true,
      session: false,
      storeId: null,
      value: '1',
    },
    {
      domain: '.x.com',
      expirationDate: 1788622242.432835,
      hostOnly: false,
      httpOnly: false,
      name: 'guest_id_ads',
      path: '/',
      sameSite: 'no_restriction',
      secure: true,
      session: false,
      storeId: null,
      value: 'v1%3A175397506971555740',
    },
    {
      domain: '.x.com',
      expirationDate: 1788622242.432881,
      hostOnly: false,
      httpOnly: false,
      name: 'guest_id_marketing',
      path: '/',
      sameSite: 'no_restriction',
      secure: true,
      session: false,
      storeId: null,
      value: 'v1%3A175397506971555740',
    },
    {
      domain: '.x.com',
      expirationDate: 1786616548.605635,
      hostOnly: false,
      httpOnly: true,
      name: 'kdt',
      path: '/',
      sameSite: null,
      secure: true,
      session: false,
      storeId: null,
      value: 'wmCaLlk5x63Nn2JbyL5v3XNYylwjG0zgegOojDXA',
    },
    {
      domain: '.x.com',
      expirationDate: 1788160649.722909,
      hostOnly: false,
      httpOnly: false,
      name: 'personalization_id',
      path: '/',
      sameSite: 'no_restriction',
      secure: true,
      session: false,
      storeId: null,
      value: '"v1_OBHYNOXm0pSI6oouP3G7Ng=="',
    },
  ],
]
// 创建多个 X 客户端
async function createXClients() {
  const clients = []

  for (let i = 0; i < cookieList.length; i++) {
    const cookies = cookieList[i]
    const cookieObj = cookies.reduce((obj, cookie) => {
      obj[cookie.name] = cookie.value
      return obj
    }, {} as Record<string, string>)

    try {
      const api = new TwitterOpenApi()
      const client = await api.getClientFromCookies(cookieObj)
      clients.push(client)
      logger.info(`X Client ${i + 1} created successfully`)
    }
    catch (error) {
      logger.error(`Failed to create X Client ${i + 1}:`, error)
    }
  }

  return clients
}

// 全局客户端数组
let xClients: TwitterOpenApiClient[] = []
let currentClientIndex = 0

// 初始化客户端
export async function initializeXClients() {
  xClients = await createXClients()
  logger.info(`初始化 ${xClients.length} 个 X clients`)
}

// 获取下一个可用客户端（轮询）
export function getNextXClient() {
  if (xClients.length === 0) {
    throw new Error('No X clients available')
  }

  const client = xClients[currentClientIndex]
  currentClientIndex = (currentClientIndex + 1) % xClients.length
  return client
}

export async function getXDetail(tweetId: string) {
  const client = getNextXClient()
  const { data } = await client.getTweetApi().getTweetDetail({
    focalTweetId: tweetId,
  })

  if (data.data.length) {
    const res = find(data.data, { tweet: { restId: tweetId } })
    // res?.tweet.legacy?.entities.media[0].videoInfo?.variants.
    // todo 数据库直接保存 media
    // 然后 数据库分别定义 low MEDIUM High 来保存对应质量的 fileId[] 直接放 media group 发送
    return pick(<TweetApiUtilsData>res, ['tweet', 'user'])
  }

  return undefined
}
