import type { Prisma, RechargeRecord, Telegram } from '@prisma/client'
import { addMinutes } from 'date-fns'
import prisma from '#root/db/index.js'
import { deleteUserSessionByTelegramId, updateMembershipExpiresAt } from '#root/db/user.js'

/**
 * 创建充值记录
 * @param data 充值记录数据
 * @returns 创建的充值记录
 */
export async function createRechargeRecord(data: {
  telegramId: string
  receiverAddress: string
  amountUSDT: number
  months: number
}): Promise<RechargeRecord> {
  const expiresAt = addMinutes(new Date(), 10)

  return prisma.rechargeRecord.create({
    data: {
      ...data,
      expiresAt,
    },
  })
}

/**
 * 更新充值记录状态
 * @param id 充值记录ID
 * @param status 新状态
 * @returns 更新后的充值记录
 */
export async function updateRechargeRecordStatus(
  id: string,
  status: 'pending' | 'completed' | 'expired' | 'failed',
): Promise<RechargeRecord> {
  return prisma.rechargeRecord.update({
    where: { id },
    data: { status },
  })
}

/**
 * 检查是否存在相同金额的待处理充值记录
 * @param amountUSDT USDT金额
 * @returns 是否存在
 */
export async function checkDuplicateRechargeAmount(amountUSDT: number): Promise<boolean> {
  const record = await prisma.rechargeRecord.findFirst({
    where: {
      amountUSDT,
      status: 'pending',
    },
  })

  return !!record
}

/**
 * 获取用户的充值记录
 * @param telegramId 用户的Telegram ID
 * @param limit 限制返回的记录数量
 * @returns 充值记录列表
 */
export async function getUserRechargeRecords(telegramId: string, limit = 10): Promise<RechargeRecord[]> {
  return prisma.rechargeRecord.findMany({
    where: {
      telegramId,
    },
    orderBy: {
      createdAt: 'desc',
    },
    take: limit,
  })
}

/**
 * 获取所有待处理的充值记录
 * @returns 待处理的充值记录列表
 */
export async function getPendingRechargeRecords(): Promise<RechargeRecord[]> {
  return prisma.rechargeRecord.findMany({
    where: {
      status: 'pending',
    },
    orderBy: {
      createdAt: 'desc',
    },
  })
}

/**
 * 检查并更新过期的充值记录
 * 将所有已过期但状态仍为pending的记录更新为expired
 * @returns 更新的记录数量
 */
export async function checkAndUpdateExpiredRecharges(): Promise<number> {
  const now = new Date()

  // 查找所有已过期但状态仍为pending的记录
  const expiredRecords = await prisma.rechargeRecord.findMany({
    where: {
      status: 'pending',
      expiresAt: {
        lt: now,
      },
    },
  })

  if (expiredRecords.length === 0) {
    return 0
  }

  // 批量更新过期记录的状态
  const result = await prisma.rechargeRecord.updateMany({
    where: {
      id: {
        in: expiredRecords.map(record => record.id),
      },
    },
    data: {
      status: 'expired',
    },
  })

  return result.count
}

/**
 * 检查是否存在相同txHash的充值记录
 * @param txHash 交易哈希
 * @returns 是否存在
 */
export async function checkDuplicateRechargeTxhash(txHash: string): Promise<boolean> {
  const record = await prisma.rechargeRecord.findFirst({
    where: {
      txHash,
    },
  })

  return !!record
}

/**
 * 根据USDT金额查找待处理的充值记录
 * @param amountUSDT USDT金额
 * @returns 匹配的充值记录
 */
export async function findPendingRechargeByAmount(amountUSDT: number): Promise<RechargeRecord | null> {
  return prisma.rechargeRecord.findFirst({
    where: {
      amountUSDT,
      status: 'pending',
    },
  })
}

/**
 * 完成充值记录并更新交易信息和会员状态
 * @param id 充值记录ID
 * @param transferAddress 转账地址
 * @param txHash 交易哈希
 * @returns 更新后的充值记录
 */
export async function completeRechargeWithTransaction(
  id: string,
  transferAddress: string,
  txHash: string,
): Promise<{
    rechargeRecord: RechargeRecord
    membership: Partial<Prisma.MembershipGetPayload<{ include: { telegram: true } }>>
  }> {
  // 使用事务确保数据一致性
  return prisma.$transaction(async (tx) => {
    // 1. 获取充值记录
    const record = await tx.rechargeRecord.findUnique({
      where: { id },
    })

    if (!record || record.status !== 'pending') {
      throw new Error('充值记录不存在或状态不是待处理')
    }

    // 2. 更新充值记录状态和交易信息
    const updatedRecord = await tx.rechargeRecord.update({
      where: { id },
      data: {
        status: 'completed',
        transferAddress,
        txHash,
      },
    })

    // 3. 更新会员状态
    const membership = await updateMembershipExpiresAt(record.telegramId, record.months)

    // 4. 删除 bot 缓存
    await deleteUserSessionByTelegramId(record.telegramId)

    return {
      rechargeRecord: updatedRecord,
      membership,
    }
  })
}
