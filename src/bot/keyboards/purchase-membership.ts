import { InlineKeyboard } from 'grammy'
import { chunk } from 'lodash-es'
import { lemonSqueezySetup } from '@lemonsqueezy/lemonsqueezy.js'
import type { Context } from '#root/bot/context.js'
import { isDevEnv } from '#root/config.js'
import { purchaseMembershipData, purchaseMembershipStepData } from '#root/bot/callback-data/purchase-membership.js'

const apiKey = isDevEnv
  ? '*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
  : '**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'

lemonSqueezySetup({
  apiKey,
  onError: error => console.error('lemonSqueezy Error!', error),
})
// 测试产品 id
const productListDev = [
  {
    id: '247723',
    month: '1',
    price: 2,
    currency: 'USD',
  },
  {
    id: '254713',
    month: '12',
    price: 20,
    currency: 'USD',
  },
]
const productListPro = [
  {
    id: '253000',
    month: '1',
    price: 2,
    currency: 'USD',
  },
  {
    id: '253009',
    month: '12',
    price: 20,
    currency: 'USD',
  },
]
export const productList = isDevEnv ? productListDev : productListPro

export async function createPurchaseMembershipKeyboard(ctx: Context) {
  return InlineKeyboard.from([
    [{
      text: ctx.t('button-purchase'),
      callback_data: purchaseMembershipStepData.pack({
        // 返回选择计划步骤
        step: 'select',
      }),
    }],
    [{
      text: `${ctx.t('button-purchase')} 💎USDT`,
      callback_data: purchaseMembershipStepData.pack({
        step: 'select-usdt',
      }),
    }],
    [{
      text: ctx.t('button-close'),
      callback_data: purchaseMembershipStepData.pack({
        step: 'close',
      }),
    }],
  ])
}

/** 购买会员 - 选择计划 */
export async function createPurchaseMembershipSelectKeyboard(ctx: Context) {
  const buttons = productList.map((product) => {
    const { id, month } = product
    const emoji = {
      1: '🌙',
      12: '🌕',
    }[month]
    return {
      text: `${emoji} ${month} ${ctx.t('purchase-month')}`,
      callback_data: purchaseMembershipData.pack({
        productId: id,
        month,
      }),
    }
  })

  const other = [
    {
      text: ctx.t('button-back'),
      callback_data: purchaseMembershipStepData.pack({
        // 返回第一步骤
        step: 'plan',
      }),
    },
    {
      text: ctx.t('button-close'),
      callback_data: purchaseMembershipStepData.pack({
        step: 'close',
      }),
    },
  ]
  return InlineKeyboard.from(
    chunk([...buttons, ...other], 1),
  )
}

/** 购买会员 - 选择计划USDT */
export async function createPurchaseMembershipSelectUsdtKeyboard(ctx: Context) {
  const buttons = [
    {
      text: `🌙 1 ${ctx.t('purchase-month')}`,
      callback_data: purchaseMembershipData.pack({
        productId: 'usdt',
        month: '1',
      }),
    },
    {
      text: `🌗 6 ${ctx.t('purchase-month')}`,
      callback_data: purchaseMembershipData.pack({
        productId: 'usdt',
        month: '6',
      }),
    },
    {
      text: `🌕 12 ${ctx.t('purchase-month')}`,
      callback_data: purchaseMembershipData.pack({
        productId: 'usdt',
        month: '12',
      }),
    },
  ]

  const other = [
    {
      text: ctx.t('button-back'),
      callback_data: purchaseMembershipStepData.pack({
        // 返回第一步骤
        step: 'plan',
      }),
    },
    {
      text: ctx.t('button-close'),
      callback_data: purchaseMembershipStepData.pack({
        step: 'close',
      }),
    },
  ]

  return InlineKeyboard.from(
    chunk([...buttons, ...other], 1),
  )
}

/** 购买会员 - 购买链接 */
export async function createPurchaseMembershipPayKeyboard(ctx: Context, url: string) {
  return InlineKeyboard.from([
    [InlineKeyboard.url(ctx.t('button-pay'), url)],
    [{
      text: ctx.t('button-back'),
      callback_data: purchaseMembershipStepData.pack({
        // 返回选择计划步骤
        step: 'select',
      }),
    }],
  ])
}
