import { Composer } from 'grammy'
import { createCheckout } from '@lemonsqueezy/lemonsqueezy.js'
import { format } from 'date-fns'
import { limit } from '@grammyjs/ratelimiter'
import type { Context } from '#root/bot/context.js'
import { logHandle } from '#root/bot/helpers/logging.js'
import { createPurchaseMembershipKeyboard, createPurchaseMembershipPayKeyboard, createPurchaseMembershipSelectKeyboard, createPurchaseMembershipSelectUsdtKeyboard, productList } from '#root/bot/keyboards/purchase-membership.js'
import { purchaseMembershipData, purchaseMembershipStepData } from '#root/bot/callback-data/purchase-membership.js'
import { generateRandomDecimal, isMembershipActive, silentFn } from '#root/bot/helpers/tools.js'
import { getMembershipByTelegramId } from '#root/db/user.js'
import { WALLET_ADDRESS } from '#root/db/tron.js'
import { createRechargeRecord } from '#root/db/recharge.js'

// // 一级菜单
// const purchaseMenu = new Menu<Context>('purchase-menu')
//   .submenu(
//     ctx => ctx.t('button-purchase'),
//     'purchase-select-menu',
//     async (ctx) => {
//       await ctx.editMessageText(ctx.t('purchase-select'))
//     },
//   )
// // 二级菜单
// const purchaseSelectMenu = new Menu<Context>('purchase-select-menu')
// purchaseSelectMenu.dynamic((_, range) => {
//   forEach(productList, (item) => {
//     range.text({
//       text: `${item.month} Months - ${item.price} ${item.currency}`,
//       payload: item.id,
//     }, async (ctx) => {
//       await ctx.menu.nav('purchase-pay-menu')
//       await ctx.editMessageText(ctx.t('purchase-confirm', {
//         date: item.month,
//         money: item.price,
//       }))
//     }).row()
//   })
// })
//   .back(
//     ctx => ctx.t('button-back'),
//     async (ctx) => {
//       await ctx.editMessageText(ctx.t('purchase-plan'))
//     },
//   )
// // 三级菜单
// const purchasePayMenu = new Menu<Context>('purchase-pay-menu')
// purchasePayMenu.url(
//   ctx => ctx.t('button-pay'),
//   async (ctx) => {
//     const productId = ctx.match!
//     const month = productList.find(item => item.id === productId)
//     const res = await createCheckout(25412, +productId, {
//       checkoutData: {
//         custom: {
//           month: String(month),
//           chat_id: String(ctx.chat?.id),
//           message_id: String(ctx.msgId),
//           telegram_id: String(ctx.from?.id),
//         },
//       },
//       testMode: true,
//     })
//     const checkoutUrl = res.data!.data.attributes.url
//     return checkoutUrl
//   },
// )
//   .row()
//   .back(
//     ctx => ctx.t('button-back'),
//     async (ctx) => {
//       await ctx.editMessageText(ctx.t('purchase-select'))
//     },
//   )

// purchaseMenu.register(purchaseSelectMenu)
// purchaseSelectMenu.register(purchasePayMenu)

const composer = new Composer<Context>()

const feature = composer.chatType('private')
feature.use(limit({
  timeFrame: 2000,
  limit: 1,
  keyGenerator: (ctx) => {
    return ctx.from?.id.toString()
  },
}))
// feature.use(purchaseMenu)
feature.command('my', logHandle('command-my'), async (ctx) => {
  const membership = await getMembershipByTelegramId(String(ctx.from.id))

  return ctx.reply(
    isMembershipActive(membership.expiresAt)
      ? ctx.t('my-premium', {
          tgId: `<code>${ctx.from.id}</code>`,
          utcExpiresAt: format(membership.expiresAt!, 'yyyy-MM-dd HH:mm:ss'),
        })
      : ctx.t('my-normal', {
          tgId: `<code>${ctx.from.id}</code>`,
          twitterDownloadsLeft: membership.twitterDownloadsLeft,
          twitterBatchDownloadsLeft: membership.twitterBatchDownloadsLeft,
        }),
    { parse_mode: 'HTML' },
  )
})

feature.command('purchase', logHandle('command-purchase'), async (ctx) => {
  return ctx.reply(ctx.t('purchase-plan'), {
    reply_markup: await createPurchaseMembershipKeyboard(ctx),
  })
})

feature.callbackQuery(
  purchaseMembershipStepData.filter(),
  logHandle('keyboard-purchase'),
  async (ctx) => {
    const { step } = purchaseMembershipStepData.unpack(
      ctx.callbackQuery.data,
    )
    if (step === 'close') {
      await ctx.deleteMessage().catch(silentFn)
    }
    if (step === 'plan') {
      await ctx.editMessageText(ctx.t('purchase-plan'), {
        reply_markup: await createPurchaseMembershipKeyboard(ctx),
      }).catch(silentFn)
    }
    if (step === 'select-usdt') {
      await ctx.editMessageText(ctx.t('purchase-select-usdt'), {
        reply_markup: await createPurchaseMembershipSelectUsdtKeyboard(ctx),
      }).catch(silentFn)
    }
    if (step === 'select') {
      await ctx.editMessageText(ctx.t('purchase-select'), {
        reply_markup: await createPurchaseMembershipSelectKeyboard(ctx),
      }).catch(silentFn)
    }
  },
)
feature.callbackQuery(
  purchaseMembershipData.filter(),
  logHandle('keyboard-purchase-pay'),
  async (ctx) => {
    const { productId, month } = purchaseMembershipData.unpack(
      ctx.callbackQuery.data,
    )
    if (productId === 'usdt') {
      const monthPriceMap = { 1: 2, 6: 11, 12: 20 } as any
      const baseAmount = monthPriceMap[month]
      const amountUSDT = generateRandomDecimal(baseAmount)
      const order = await createRechargeRecord({
        telegramId: String(ctx.from?.id),
        months: +month,
        amountUSDT,
        receiverAddress: WALLET_ADDRESS,
      })

      await ctx.editMessageText(ctx.t('purchase-confirm-usdt', {
        orderId: order.id,
        date: month,
        money: String(amountUSDT),
        usdtAddress: WALLET_ADDRESS,
      }), {
        parse_mode: 'HTML',
      })
      return
    }

    const res = await createCheckout(25412, +productId, {
      checkoutData: {
        custom: {
          month: String(month),
          chat_id: String(ctx.chat?.id),
          message_id: String(ctx.msgId),
          telegram_id: String(ctx.from?.id),
        },
      },
      testMode: true,
    })
    const checkoutUrl = res.data!.data.attributes.url
    if (!checkoutUrl)
      return
    const product = productList.find(item => item.id === productId)
    await ctx.editMessageText(ctx.t('purchase-confirm', {
      date: month,
      money: product!.price,
    }), {
      reply_markup: await createPurchaseMembershipPayKeyboard(ctx, checkoutUrl),
    }).catch(silentFn)
  },
)

export { composer as purchaseFeature }
