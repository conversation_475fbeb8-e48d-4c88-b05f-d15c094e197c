import { pick } from 'lodash-es'
import { type Membership, MembershipLevel, type Telegram } from '@prisma/client'
import { addMonths, isAfter } from 'date-fns'
import prisma from '#root/db/index.js'

// 用户 session
export async function getUserSessionByTelegramId(telegramId: string) {
  return await prisma.session.findUnique({ where: { key: telegramId } })
}
export async function deleteUserSessionByTelegramId(telegramId: string) {
  return await prisma.session.delete({ where: { key: telegramId } })
}

export async function getAllUser() {
  return await prisma.telegram.findMany({
    select: {
      id: true,
      telegramId: true,
    },
  })
}
/**
 * 创建或者更新 tg user
 * @param payload tg数据
 */
export async function upsertUser(payload: Omit<Telegram, 'id' | 'createdAt' | 'updateAt'>) {
  const data = pick(payload, ['telegramId', 'first_name', 'username', 'last_name', 'language_code', 'is_bot', 'is_premium'])
  const user = await prisma.telegram.upsert({
    where: {
      telegramId: data.telegramId,
    },
    create: {
      ...data,
      membership: {
        create: {},
      },
    },
    update: data,
  })
  return user
}

/**
 * 更新用户设置
 * @description 用户相关的一些设置都放在这个函数来更新
 * @param userId
 * @param payload current_language | react on/off | ...
 */
export async function updateUserSettingsById(userId: string, payload: Pick<Partial<Telegram>, 'current_language'>) {
  return await prisma.telegram.update({
    where: { id: userId },
    data: payload,
  })
}

export async function getMembershipByTelegramId(telegramId: string) {
  const membership = await prisma.membership.findUnique({
    where: { telegramId },
  })
  if (!membership) {
    return await prisma.membership.create({
      data: {
        telegramId,
      },
    })
  }
  return membership
}

export async function updateMembershipByTelegramId(telegramId: string, payload: Partial<Membership>) {
  return await prisma.membership.update({
    where: { telegramId },
    data: payload,
    select: {
      id: true,
      telegramId: true,
      expiresAt: true,
      updatedAt: true,
      telegram: true,
    },
  })
}

/**
 * 更新会员过期时间
 * @param telegramId
 * @param month
 */
export async function updateMembershipExpiresAt(telegramId: string, month: number) {
  const membership = await getMembershipByTelegramId(telegramId)
  // 获取当前日期
  const now = new Date()
  // 有expiresAt 并且没过期从当前expiresAt 时间开始累加 否则重新开始算
  const preDate = !!membership.expiresAt && !isAfter(now, new Date(membership.expiresAt))
    ? new Date(membership.expiresAt)
    : now
  const newExpiresAt = addMonths(preDate, month)
  // todo SPONSOR 和 PATRON 区分
  return await updateMembershipByTelegramId(telegramId, { expiresAt: newExpiresAt, level: MembershipLevel.SUPPORTER })
}

/**
 * 更新会员额度
 * @param telegramId
 * @param _amount
 */
export async function updateMembershipQuota(telegramId: string, _amount: number = 0) {
  // 先取消额度限制
  const membership = await getMembershipByTelegramId(telegramId)
  return membership
  // const shouldUseExtraQuota = membership.extraQuota > 0 && membership.effectiveQuota <= 0
  // return await prisma.$transaction(async (tx) => {
  //   await tx.usage.create({
  //     data: {
  //       telegramId,
  //       amount,
  //     },
  //   })
  //   return await tx.membership.update({
  //     where: { telegramId },
  //     data: {
  //       effectiveQuota: !shouldUseExtraQuota
  //         ? { decrement: amount }
  //         : undefined, // 明确不更新该字段
  //       extraQuota: shouldUseExtraQuota
  //         ? { decrement: amount }
  //         : undefined,
  //     },
  //   })
  // })
}

/**
 * 更新所有用户的额度
 */
export async function updateMembershipsCredit() {
  const oneGB = 1024 ** 3
  // 步骤1：重置所有用户的基础额度
  await prisma.$executeRaw`
    UPDATE "Membership"
    SET 
      "dailyRoll" = 5,
      "baseDailyQuota" = CASE
      WHEN level = 'FREE' THEN ${oneGB}
      WHEN level = 'SUPPORTER' THEN ${5 * oneGB}
      WHEN level = 'SPONSOR' THEN ${20 * oneGB}
      WHEN level = 'PATRON' THEN ${50 * oneGB}
      ELSE "baseDailyQuota"
    END,
    "twitterDownloadsLeft" = CASE
      WHEN level = 'FREE' THEN 20
      ELSE "twitterDownloadsLeft"
    END,
    "twitterBatchDownloadsLeft" = CASE
      WHEN level = 'FREE' THEN 3
      ELSE "twitterBatchDownloadsLeft"
    END
  `

  // 步骤2：重置当日可用额度
  return await prisma.$executeRaw`
    UPDATE "Membership"
    SET "effectiveQuota" = "baseDailyQuota" + "extraQuota"
    WHERE "effectiveQuota" != "baseDailyQuota" + "extraQuota"
  `
}
