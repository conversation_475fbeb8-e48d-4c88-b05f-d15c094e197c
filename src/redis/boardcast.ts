import type { Job } from 'bullmq'
import { Queue, Worker } from 'bullmq'
import { chunk } from 'lodash-es'
import { _botClient } from '#root/main.js'
import { getAllUser } from '#root/db/user.js'
import { redisConfig } from '#root/redis/client.js'
import { logger } from '#root/logger.js'

// 定义广播任务类型
interface BroadcastJobData {
  sourceChatId: string | number
  sourceMsgId: string | number
  targetChatId: string | number
}
let successCount = 0
let failedCount = 0
let totalCount = 0

const broadcastQueueName = 'broadcastQueue'

export const broadcastQueue = new Queue(broadcastQueueName, {
  connection: redisConfig,
  defaultJobOptions: {
    attempts: 0,
    backoff: {
      type: 'fixed',
      delay: 2000,
    },
  },
})

function resetCounts() {
  successCount = 0
  failedCount = 0
  totalCount = 0
}

export const broadcastWorker = new Worker(
  broadcastQueueName,
  async (job: Job<BroadcastJobData>) => {
    const { sourceChatId, sourceMsgId, targetChatId } = job.data
    logger.debug(`Processing broadcast job for user ${targetChatId}`)

    if (!_botClient) {
      logger.error('Bot client is not available')
      failedCount = failedCount + 1
      return
    }

    try {
      await _botClient.api.copyMessage(targetChatId, sourceChatId, Number(sourceMsgId))
      successCount = successCount + 1
      logger.debug(`Successfully sent broadcast to user ${targetChatId}`)
    }
    catch (error: any) {
      failedCount = failedCount + 1
      logger.warn(`Failed to send broadcast to user ${targetChatId}: ${error.message}`)
    }
    finally {
      if (successCount + failedCount === totalCount) {
        const text = `📢 广播发送完毕\n\n成功: ${successCount}\n失败: ${failedCount}\n总数: ${totalCount}`
        logger.info(`Broadcast completed: ${successCount} success, ${failedCount} failed, ${totalCount} total`)
        _botClient?.api.sendMessage('-1002082345958', text).catch(() => void 0)
        resetCounts()
      }
    }
  },
  {
    connection: redisConfig,
    // 并发 20
    concurrency: 20,
    removeOnComplete: { count: 0 },
    // 每秒钟最多处理 25 条 避免 tg API 每秒最多30 条的速率限制
    limiter: { max: 25, duration: 1500 },
  },
)

// 添加 Worker 事件监听器
broadcastWorker.on('ready', () => {
  logger.info('📢 Broadcast worker is ready')
})

broadcastWorker.on('error', (error) => {
  logger.error('Broadcast worker error:', error)
})

broadcastWorker.on('failed', (job, error) => {
  logger.error(`Broadcast job ${job?.id} failed:`, error)
})

/**
 * 向所有用户广播消息
 */
export async function broadcastMessageToAllUsers(sourceChatId: string | number, sourceMsgId: string | number) {
  try {
    logger.info(`Starting broadcast from chat ${sourceChatId}, message ${sourceMsgId}`)

    const users = await getAllUser()
    // 测试数据
    // const users = [{ telegramId: '6790673213' }, { telegramId: '5546272954' }, { telegramId: '6387933446' }]

    const allUserChatIds = users.map(user => user.telegramId)
    totalCount = allUserChatIds.length

    logger.info(`Found ${totalCount} users for broadcast`)

    if (totalCount === 0) {
      return { success: false, count: 0, message: '没有找到用户进行广播' }
    }

    const list = chunk(allUserChatIds, 2000)
    for (const tgIds of list) {
      const bulkJob = tgIds.map(tgId => ({
        name: broadcastQueueName,
        data: { sourceChatId, sourceMsgId, targetChatId: tgId },
      }))
      await broadcastQueue.addBulk(bulkJob)
    }

    logger.info(`Added ${totalCount} broadcast jobs to queue`)
    return { success: true, count: allUserChatIds.length, message: '广播任务已添加到队列' }
  }
  catch (error: any) {
    logger.error('Failed to add broadcast jobs to queue:', error)
    return { success: false, message: `广播任务添加到队列失败: ${error.message}` }
  }
}
