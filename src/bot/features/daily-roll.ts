import { Composer } from 'grammy'
import type { Context } from '#root/bot/context.js'
import { logHandle } from '#root/bot/helpers/logging.js'
import { silentFn } from '#root/bot/helpers/tools.js'
import { deleteUserSessionByTelegramId, getMembershipByTelegramId, updateMembershipByTelegramId } from '#root/db/user.js'

const composer = new Composer<Context>()

const feature = composer.chatType('private')

feature.command('dailyroll', logHandle('command-dailyroll'), async (ctx) => {
  const membership = await getMembershipByTelegramId(String(ctx.from.id))
  if (membership.dailyRoll <= 0) {
    return await ctx.reply(ctx.t('dailyroll-no-usage'))
  }
  const dice = ['🎲', '🎯', '🏀', '⚽', '🎳']
  const msg = await ctx.replyWithDice(dice[Math.floor(Math.random() * dice.length)])
  if (msg.dice) {
    await updateMembershipByTelegramId(String(ctx.from.id), {
      twitterDownloadsLeft: membership.twitterDownloadsLeft + msg.dice.value,
      dailyRoll: membership.dailyRoll - 1,
    })
    await deleteUserSessionByTelegramId(String(ctx.from.id)).catch(silentFn)
    msg.react('🎉', { is_big: true })
  }
  setTimeout(() => {
    ctx.reply(ctx.t('dailyroll-get', { value: msg.dice.value })).catch(silentFn)
  }, 3000)
  return msg
})

export { composer as dailyrollFeature }
