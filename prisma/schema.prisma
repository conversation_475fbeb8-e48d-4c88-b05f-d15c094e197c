// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum VideoQuality {
  low
  medium
  high
}

model Telegram {
  id               String           @id @default(nanoid())
  telegramId       String           @unique
  createdAt        DateTime         @default(now())
  updatedAt        DateTime         @updatedAt
  is_bot           Boolean          @default(false)
  is_premium       Boolean?         @default(false)
  first_name       String
  last_name        String?
  username         String?
  language_code    String?
  // 用户设定----
  current_language String?
  membership       Membership?
  usage            Usage[]
  rechargeRecord   RechargeRecord[]
  videoQuality     VideoQuality     @default(high)
}

model Session {
  id    Int    @id @default(autoincrement())
  // tg id
  key   String @unique
  value String
}

enum MembershipLevel {
  FREE // 普通用户
  SUPPORTER // 🌱 支持者版 $2
  SPONSOR // 🌳 赞助者版（中等额度）$5
  PATRON // 👼 守护者版 🛡️ $12
}

enum RechargeStatus {
  pending
  completed
  failed
  expired
}

model Membership {
  id                        String          @id @default(nanoid())
  telegramId                String          @unique
  telegram                  Telegram        @relation(fields: [telegramId], references: [telegramId])
  createdAt                 DateTime        @default(now())
  updatedAt                 DateTime        @updatedAt
  level                     MembershipLevel @default(FREE)
  // 到期时间
  expiresAt                 DateTime?
  twitterDownloadsLeft      Int             @default(20)
  twitterBatchDownloadsLeft Int             @default(3)
  // 每日投掷次数
  dailyRoll                 Int             @default(3)
  // 每日大文件额度 
  // 根据会员等级变化： 普通 1G 付费用户 5G 高级用户 20G
  baseDailyQuota            BigInt          @default(1073741824)
  // 流量包叠加额度 仅限会员 永久有效
  extraQuota                BigInt          @default(0)
  // 实际可用额度
  effectiveQuota            BigInt          @default(1073741824)
}

model Usage {
  id          String   @id @default(nanoid())
  // 关联用户
  telegramId  String
  telegram    Telegram @relation(fields: [telegramId], references: [telegramId])
  // 用量数值（根据类型可能是次数或字节数）
  amount      BigInt
  createdAt   DateTime @default(now())
  // 按自然日统计
  trackedDate DateTime @default(now())

  @@index([telegramId, trackedDate])
}

model RechargeRecord {
  id              String         @id @default(nanoid())
  telegramId      String // 用户ID
  // 充值金额USDT
  amountUSDT      Float
  months          Int // 充值月数
  receiverAddress String // 收款地址
  transferAddress String? // 转账地址
  txHash          String? // 交易哈希
  // 状态：pending(待处理), completed(已完成), failed(失败) 过期(expired)
  status          RechargeStatus @default(pending)
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt
  expiresAt       DateTime

  // 关联用户
  telegram Telegram @relation(fields: [telegramId], references: [telegramId], onDelete: Cascade)
}

model TweetDetail {
  id          String   @id @default(nanoid())
  tweetId     String   @unique
  url         String
  // 旧 待删除
  sourceTweet Json?
  count       Int      @default(1)
  // 旧 待删除
  mediaGroups Json     @default("[]")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  // 新源数据
  data        Json?
  // 没有 media 就是纯文本Tweet
  media       Json     @default("[]")
  low         Json     @default("[]")
  medium      Json     @default("[]")
  high        Json     @default("[]")
}

model UserTweets {
  id        String   @id @default(nanoid(4))
  createdAt DateTime @default(now())
  // 到期时间
  expiresAt DateTime
  userId    String
  result    Json
  cursor    String

  @@unique([userId, cursor])
}
