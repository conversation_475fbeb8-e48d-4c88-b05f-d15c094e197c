import process from 'node:process'
import path from 'node:path'
import { I18n } from '@grammyjs/i18n'
import type { Context } from '#root/bot/context.js'

export const i18n = new I18n<Context>({
  defaultLocale: 'en',
  directory: path.resolve(process.cwd(), 'locales'),
  useSession: true,
  fluentBundleOptions: {
    useIsolating: false,
  },
  // 定义全局可用的占位符
  globalTranslationContext(ctx) {
    return { name: ctx.from?.first_name ?? '' }
  },
})

export const isMultipleLocales = i18n.locales.length > 1
