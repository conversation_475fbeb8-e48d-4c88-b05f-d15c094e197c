import { Hono } from 'hono'
import { format } from 'date-fns'
import { Reactions } from '@grammyjs/emoji'
import { deleteUserSessionByTelegramId, updateMembershipExpiresAt } from '#root/db/user.js'
import { _botClient } from '#root/main.js'
import { i18n } from '#root/bot/i18n.js'

const lemonsqueezy = new Hono()

lemonsqueezy.post('/webhook', async (c) => {
  const rawBody = await c.req.json()
  const { meta } = rawBody
  if (meta.event_name === 'order_created') {
    const { month, chat_id, message_id, telegram_id } = meta.custom_data
    const res = await updateMembershipExpiresAt(String(telegram_id), +month)
    deleteUserSessionByTelegramId(String(telegram_id)).catch(() => void 0)
    if (!_botClient)
      return
    // 发送到监控群
    _botClient.api.sendMessage('-1002082345958', `🎊\n用户: ${telegram_id} - @${res.telegram.username} 购买成功\n month:${month}`).catch(() => void 0)
    _botClient.api.deleteMessage(chat_id, message_id).catch(() => void 0)
    // 发送用户通知
    const expiresAt = format(res.expiresAt!, 'yyyy-MM-dd HH:mm:ss')
    // todo 发送庆祝贴纸
    _botClient.api.sendMessage(
      telegram_id,
      i18n.translate(res.telegram.current_language ?? res.telegram.language_code ?? 'en', 'purchase-success', {
        utcExpiresAt: expiresAt,
      }),
      { parse_mode: 'HTML' },
    )
      .then((msg) => {
        _botClient!.api.setMessageReaction(
          chat_id,
          msg.message_id,
          [{
            type: 'emoji',
            emoji: Reactions.party_popper,
          }],
          {
            is_big: true,
          },
        ).catch(() => void 0)
      })
      .catch(() => void 0)
    return c.json({ status: true })
  }
  return c.json({ status: false })
})

lemonsqueezy.get('/', (c) => {
  return c.json({ status: true })
})

export default lemonsqueezy
