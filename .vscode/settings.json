{"typescript.preferences.importModuleSpecifier": "non-relative", "typescript.tsdk": "node_modules/typescript/lib", "eslint.codeActionsOnSave.mode": "problems", "eslint.format.enable": true, "eslint.useFlatConfig": true, "[typescript]": {"editor.formatOnSave": true, "editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[markdown]": {"editor.formatOnSave": true, "editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[javascript]": {"editor.formatOnSave": true, "editor.defaultFormatter": "dbaeumer.vscode-eslint"}}