import { InlineKeyboard } from 'grammy'
import type { UserTweets } from '@prisma/client'
import type { Context } from '#root/bot/context.js'
import { downloadData, downloadPageData, manualDownloadData } from '#root/bot/callback-data/download.js'
import type { UserTweetsType } from '#root/bot/helpers/twitter.js'

export async function createDownloadKeyboard(ctx: Context, payload: UserTweets) {
  const { id } = payload
  const tRes = payload.result as UserTweetsType
  const pageBtns = []
  if (!tRes.isFirstPage) {
    pageBtns.push({
      text: '«',
      callback_data: downloadPageData.pack({ userTweetsId: id, btnName: 'top' }),
    })
  }
  pageBtns.push({
    text: '»',
    callback_data: downloadPageData.pack({ userTweetsId: id, btnName: 'bottom' }),
  })
  return InlineKeyboard.from([
    pageBtns,
    [
      {
        text: 'download',
        callback_data: downloadData.pack({ userTweetsId: id }),
      },
    ],
  ])
}

// 创建手动下载按钮
export async function createManualDownloadKeyboard(ctx: Context, payload: { tweetId: string }) {
  const { tweetId } = payload
  return InlineKeyboard.from([
    [
      {
        text: 'Manual Download 👆',
        callback_data: manualDownloadData.pack({ tweetId }),
      },
    ],
  ])
}
