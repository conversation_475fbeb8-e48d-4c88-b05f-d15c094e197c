import { InlineKeyboard } from 'grammy'
import type { Context, MyConversation } from '#root/bot/context.js'
import { createSendAdsKeyboard } from '#root/bot/keyboards/ads.js'

export async function sendBroadcast(conversation: MyConversation, ctx: Context) {
  // 发起对话，提示用户发送消息
  await ctx.reply('请发送一条消息广告消息')

  // 等待用户发送任意消息
  const userMsg = await conversation.waitFor('message')

  // 获取消息 ID
  const messageId = userMsg.msg.message_id

  // 回复包含按钮的消息
  await ctx.reply(`是否要发送广播：${messageId}`, {
    reply_markup: await createSendAdsKeyboard(String(userMsg.chat.id), String(messageId)),
  })
}
