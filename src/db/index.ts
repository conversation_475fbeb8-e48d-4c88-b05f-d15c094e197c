import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient().$extends({
  query: {
    membership: {
      // 转换Membership 中的 bigint 为 number
      async $allOperations({ args, query }: any) {
        const result = await query(args)
        if (Array.isArray(result)) {
          return result.map(member => ({
            ...member,
            baseDailyQuota: Number(member.baseDailyQuota),
            extraQuota: Number(member.extraQuota),
            effectiveQuota: Number(member.effectiveQuota),
          }))
        }
        return result
          ? {
              ...result,
              baseDailyQuota: Number(result.baseDailyQuota),
              extraQuota: Number(result.extraQuota),
              effectiveQuota: Number(result.effectiveQuota),
            }
          : null
      },
    },
  },
})

export default prisma
