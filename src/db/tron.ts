import { Reactions } from '@grammyjs/emoji'
import { format } from 'date-fns'
import { checkDuplicateRechargeTxhash, completeRechargeWithTransaction, findPendingRechargeByAmount } from '#root/db/recharge.js'
import { _botClient } from '#root/main.js'
import { i18n } from '#root/bot/i18n.js'
import { isDevEnv } from '#root/config.js'

// const TRON_PRO_API_KEY = '32c8ff31-b63e-4fe6-8c08-670586baf03e' // 测试客户的
const TRON_PRO_API_KEY = 'e2eaacbe-bed7-49da-8a1b-46b3910d2391'

export const WALLET_ADDRESS = isDevEnv
  ? 'TPnqtuhfyXeuBWmcRMjUQjin6p7JV44kfP' // 测钱包
  : 'TYcnLZc9m137UpGDN6S3AV1E6RCRUi6EXZ' // 欧意子账户钱包

const TON_API_URL = isDevEnv
  ? `https://nile.trongrid.io/v1/accounts/${WALLET_ADDRESS}/transactions/trc20` // 测试网
  : `https://api.trongrid.io/v1/accounts/${WALLET_ADDRESS}/transactions/trc20` // 主网

/**
 * 获取TRC20钱包的最新交易记录·
 * @returns 处理的交易数量
 */
export async function checkAndProcessTRC20Deposits(): Promise<number> {
  try {
    // 获取最近的USDT转账记录
    const { data: transactions } = await fetch(TON_API_URL, {
      headers: {
        'TRON-PRO-API-KEY': TRON_PRO_API_KEY,
        'Content-Type': 'application/json',
      },
    }).then(res => res.json())

    let processedCount = 0

    // 处理每一笔交易
    for (const tx of transactions) {
      // 只处理转入到我们钱包的交易
      if (tx.to !== WALLET_ADDRESS) {
        continue
      }
      if (tx.block_timestamp < Date.now() - 60 * 1000 * 20) {
        // 只处理过去20分钟内的交易
        continue
      }
      // 已经有的交易 hash 不再处理
      const hasTxhash = await checkDuplicateRechargeTxhash(tx.transaction_id)
      if (hasTxhash) {
        continue
      }
      // 转换金额（USDT有6位小数）
      const amount = Number.parseFloat(tx.value) / 1000000
      // 查找匹配金额的待处理充值记录
      const record = await findPendingRechargeByAmount(amount)

      if (record) {
        // 更新充值记录状态
        const res = await completeRechargeWithTransaction(
          record.id,
          tx.from,
          tx.transaction_id,
        )
        processedCount++
        _botClient!.api.sendMessage(
          record.telegramId,
          i18n.translate(res.membership.telegram?.current_language ?? res.membership.telegram?.language_code ?? 'en', 'purchase-success', {
            utcExpiresAt: format(res.membership.expiresAt!, 'yyyy-MM-dd HH:mm:ss'),
          }),
          { parse_mode: 'HTML' },
        )
          .then((msg) => {
            _botClient!.api.setMessageReaction(
              msg.chat.id,
              msg.message_id,
              [{
                type: 'emoji',
                emoji: Reactions.party_popper,
              }],
              {
                is_big: true,
              },
            ).catch(() => void 0)
          })
          .catch(() => void 0)
        const systemText = `🎊
用户: ${res.membership.telegramId} - @${res.membership.telegram?.username} 购买成功
month:${res.rechargeRecord.months}
USDT:${res.rechargeRecord.amountUSDT}`

        _botClient!.api.sendMessage('-1002082345958', systemText).catch(() => void 0)
      }
    }

    return processedCount
  }
  catch (error) {
    console.error('处理TRC20充值失败:', error)
    return 0
  }
}
