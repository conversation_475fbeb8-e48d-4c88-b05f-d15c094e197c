import { Buffer } from 'node:buffer'
import { Composer, InputFile, InputMediaBuilder } from 'grammy'
import { every, get, isEmpty, maxBy, minBy, reduce, round, sortBy, sumBy } from 'lodash-es'
import type { InputMediaPhoto, InputMediaVideo } from 'grammy/types'
import { autoQuote } from '@roziscoding/grammy-autoquote'
import { bold, fmt, pre } from '@grammyjs/parse-mode'
import { limit } from '@grammyjs/ratelimiter'
import got from 'got'
import type { TweetApiUtilsData } from 'twitter-openapi-typescript'
import type { Media } from 'twitter-openapi-typescript-generated'
import type { Context } from '#root/bot/context.js'
import { logHandle } from '#root/bot/helpers/logging.js'
import type { UserTweetsType } from '#root/bot/helpers/twitter.js'
import { fmtUserTweets, getTweetDetail, getTweetId, queryUser, queryUserTweets, validateTwitter } from '#root/bot/helpers/twitter.js'
import { extractUrlFromText, isMembershipActive, removeLastLink, silentFn } from '#root/bot/helpers/tools.js'
import { createManyTweetDetail, createTweetDetail, createUserTweets, getTweetDetailByTweetId, getUserTweets, getUserTweetsById, updateTweetDetailByTweetId } from '#root/db/tweet.js'
import { logger } from '#root/logger.js'
import { updateMembershipByTelegramId, updateMembershipQuota } from '#root/db/user.js'
import { addTask, getAllTask, getTaskByTelegramId } from '#root/redis/download.js'
import { createMiniAppKeyboard } from '#root/bot/keyboards/miniapp.js'
import { createDownloadKeyboard, createManualDownloadKeyboard } from '#root/bot/keyboards/download.js'
import { getXDetail } from '#root/lib/x-client.js'
import { downloadData, downloadPageData, manualDownloadData } from '#root/bot/callback-data/download.js'

const twoGB = 2 * 1024 ** 3
// WEBPAGE_CURL_FAILED  可能是文件超大随后再解析就是 WEBPAGE_MEDIA_EMPTY
const overSizeError = ['error message "WEBPAGE_MEDIA_EMPTY"', 'error message "WEBPAGE_CURL_FAILED"']

const composer = new Composer<Context>()
composer.use(autoQuote())
const feature = composer.chatType(['private', 'group', 'supergroup'])
feature.use(limit({
  timeFrame: 1200,
  limit: 1,
  // 当超过限制时执行调用。
  onLimitExceeded: async (ctx) => {
    await ctx.reply('Too many requests, please slow down')
  },
  keyGenerator: (ctx) => {
    return ctx.from?.id.toString()
  },
}))

feature.command('thread', logHandle('download-thread'), async (ctx) => {
  return await ctx.reply(ctx.t('feature-updating'))
})

feature.command('dl', logHandle('command-download'), async (ctx) => {
  // todo 群组徐单独开通会员
  return await ctx.reply(ctx.t('feature-updating'))
})

feature.command('task', logHandle('download-task'), async (ctx) => {
  const userTask = await getTaskByTelegramId(String(ctx.from.id))
  const text = `Task count:${userTask.count}\nTask Total Size:${round(userTask.totalTasksSize / 1024 / 1024, 2)}MB`
  return await ctx.reply(text)
})
feature.command('alltask', logHandle('all-download-task'), async (ctx) => {
  // 管理员次才可以查看
  if (ctx.from?.id !== 6387933446)
    return
  const allTask = await getAllTask()
  const text = `Task count:${allTask.count}\nTask Total Size:${round(allTask.totalTasksSize / 1024 / 1024, 2)}MB`
  return await ctx.reply(text)
})

async function handleBatchDownloadPage(ctx: Context, payload: {
  screenName?: string
  userId?: string
  cursor?: string
  btnName?: 'top' | 'bottom' | string
}) {
  const membershipStatus = validateMembership(ctx)
  // 非会员 验证批量下载是否用完
  if (!membershipStatus.isMembership) {
    if (!membershipStatus.hasTwitterBatchDownloads) {
      if (ctx.callbackQuery) {
        return await ctx.answerCallbackQuery({
          text: ctx.t('membership-batchdownload-exhausted'),
          show_alert: true,
        })
      }
      return await ctx.reply(ctx.t('membership-batchdownload-exhausted'))
    }
  }
  const screenName = payload.screenName
  let userId = payload.userId
  try {
    if (!userId) {
      const data = await queryUser(screenName!)
      if (isEmpty(data)) {
        return await ctx.reply('❌ This account doesn’t exist')
      }
      userId = get(data, ['user_result_by_screen_name', 'result', 'rest_id'])
    }
    let userTweets = await getUserTweets(userId, payload.cursor)
    if (isEmpty(userTweets)) {
      const res = await queryUserTweets(userId, payload.cursor)
      const uTweets = fmtUserTweets(res)
      userTweets = await createUserTweets(userId, payload.cursor, uTweets)
    }
    const { tweets } = userTweets!.result as UserTweetsType
    if (isEmpty(tweets)) {
      if (payload.btnName) {
        const emptyText = payload.btnName === 'bottom' ? 'No more Tweets' : 'No previous Tweets'
        // 按钮操作则toast 提示
        return await ctx.answerCallbackQuery({
          text: emptyText,
          show_alert: true,
        })
      }
      const emptyText = '🈳The user has no tweets'
      return await ctx.reply(emptyText)
    }
    // 批量创建推文详情
    const tweetDetailList = tweets.map((tweet: any) => {
      return {
        tweetId: tweet.legacy.id_str,
        url: `https://x.com/i/status/${tweet.legacy.id_str}`,
        sourceTweet: tweet,
      }
    })
    createManyTweetDetail(tweetDetailList).catch(silentFn)
    // 推文信息
    const tweetMsg = tweets.map((tweet: any, idx: number) => {
      const textStr = tweet.legacy?.full_text?.replace(/\n/g, '')?.slice(0, 14)
      const text = textStr?.length < 14 ? textStr : `${textStr}...`
      const idxText = `${idx + 1}. ${text}`
      const string = Buffer.from(idxText, 'utf-8').toString('utf-8')
      return `<a href="https://x.com/i/status/${tweet.legacy.id_str}">${string}</a>`
    }).join('\n')

    // 媒体计数
    const mediaSummary = reduce(tweets, (res, item) => {
      item?.media?.forEach((media: any) => {
        if (media.type === 'photo') {
          res.photo += 1
        }
        if (['video', 'animated_gif'].includes(media.type)) {
          res.video += 1
        }
      })
      return res
    }, { video: 0, photo: 0 })

    const mediaSummaryMsg = `<i>🎬 <b>video:${mediaSummary.video}</b> 🖼️ <b>Photo:${mediaSummary.photo}</b></i>`
    const msg = `${tweetMsg}\n\n${mediaSummaryMsg}`

    if (payload.btnName) {
      return await ctx.editMessageText(msg, {
        parse_mode: 'HTML',
        link_preview_options: {
          is_disabled: true,
        },
        reply_markup: await createDownloadKeyboard(ctx, userTweets!),
      })
    }
    await ctx.reply(msg, {
      parse_mode: 'HTML',
      link_preview_options: {
        is_disabled: true,
      },
      reply_markup: await createDownloadKeyboard(ctx, userTweets!),
    })
  }
  catch (error: any) {
    logger.error(`批量下载分页逻辑:\n${error.message}`)
  }
}

// 处理单个下载

async function handleSingleDownload(ctx: Context, url: string, cb?: (tId: string) => void) {
  ctx.chatAction = 'typing'
  // 额度耗尽提示购买会员
  // todo 耗尽额度 普通用户给小程序链接看广告 大文件看 15 秒直接下载
  const membershipStatus = validateMembership(ctx)
  // 非会员且非批量
  if (!membershipStatus.isMembership && !cb) {
    if (!membershipStatus.hasTwitterDownloads) {
      return await ctx.reply(ctx.t('membership-exhausted'), {
        reply_markup: await createMiniAppKeyboard(ctx, url),
      })
    }
  }

  let cachedMedia
  let tweetId: string
  try {
    tweetId = await getTweetId(url)

    let tweetDetail = await getTweetDetailByTweetId(tweetId)
    console.log('%c [ tweetDetail ]-193', 'font-size:13px; background:pink; color:#bf2c9f;', tweetDetail)
    // 没有tweet记录先创建
    if (!tweetDetail) {
      const xRes = await getXDetail(tweetId)
      // 判断 xRes 如果是 undefined 直接返回 未找到推文
      if (!xRes) {
        return await ctx.reply('❌ 未找到推文')
      }
      const media = xRes?.tweet.legacy?.entities?.media ?? []
      tweetDetail = await createTweetDetail({ tweetId, url, data: xRes as any, media: media as any })
    }

    // 获取用户的视频质量设置，默认为 high
    const videoQuality = ctx.session.videoQuality || 'high'

    // 根据用户的 videoQuality 设置查看 tweetDetail 中是否有对应质量的数据
    const qualityData = tweetDetail[videoQuality] as any[]

    if (qualityData && qualityData.length > 0) {
      // 如果有对应质量的数据，直接发送
      const groups = qualityData.map((item: any) => ({
        type: item.type,
        media: item.fileId || item.url,
        caption: item?.caption,
        supports_streaming: item.supports_streaming,
      }))
      await ctx.replyWithMediaGroup(groups)
      return null
    }

    // 如果没有对应质量的数据，从 tweetDetail.media 中筛选对应质量的媒体
    const media = tweetDetail.media as unknown as Media[]
    console.log('%c [ media ]-222', 'font-size:13px; background:pink; color:#bf2c9f;', media)
    const legacy = (tweetDetail.data as unknown as Pick<TweetApiUtilsData, 'tweet' | 'user'>)?.tweet?.legacy
    if (media && media.length > 0) {
      // 筛选对应质量的媒体
      const filteredMedia = filterMediaByQuality(media, videoQuality, legacy?.fullText)
      console.log('%c [ filteredMedia ]-226', 'font-size:13px; background:pink; color:#bf2c9f;', filteredMedia)

      if (filteredMedia.length === 0) {
        // 如果是纯文本推文
        return await ctx.reply(legacy?.fullText ?? '-')
      }

      // 构建媒体组并发送
      const mediaGroups = filteredMedia.map(item => buildMediaGroup(item))
      const msg = await ctx.replyWithMediaGroup(mediaGroups)

      // 处理回复后的 fileid 并保存到对应质量字段
      const sendedMediaGroups = msg.map((item, idx) => {
        const fileId = getFileIdFromMessage(item)
        return fileId ? { ...filteredMedia[idx], fileId } : null
      }).filter(Boolean)

      // 更新对应质量的数据到数据库
      await updateTweetDetailByTweetId(tweetId, {
        [videoQuality]: sendedMediaGroups as any,
      })

      // 更新使用额度
      // await updateSessionQuota(ctx, {
      //   isSingleDownload: !cb,
      // })

      return null
    }

    // 兼容旧逻辑：存在文件id直接发送
    // if (tweetDetail.mediaGroups.length && every(tweetDetail.mediaGroups, (item: any) => item.fileId)) {
    //   // todo 判断 是否有大文件额度， 并计算 size
    //   const groups = tweetDetail.mediaGroups.map((item: any) => {
    //     return {
    //       type: item.type,
    //       media: item.fileId,
    //       caption: item?.caption,
    //       supports_streaming: item.supports_streaming,
    //     }
    //   })
    //   await ctx.replyWithMediaGroup(groups)

    //   if (ctx?.from?.id) {
    //     // ! 更新使用额度
    //     const quota = await updateMembershipQuota(
    //       String(ctx.from.id),
    //       sumBy(tweetDetail.mediaGroups, (item) => {
    //         const size = item.size ?? 0
    //         // 大于 20MB 才算算进额度
    //         return size >= 20971520 ? size : 0
    //       }),
    //     )
    //     return await updateSessionQuota(ctx, {
    //       ...quota,
    //       isSingleDownload: !cb,
    //     })
    //   }
    //   return null
    // }

    // const { media: oldMedia, legacy } = tweetDetail.sourceTweet as any
    // // tweetDetail 中有mediaGroups 就直接用 不需要重新请求
    // const mediaWidthMeta = tweetDetail?.mediaGroups?.length
    //   ? tweetDetail.mediaGroups
    //   : await buildMediaWithMeta(oldMedia)
    // if (mediaWidthMeta.length) {
    //   mediaWidthMeta[mediaWidthMeta.length - 1].caption = removeLastLink(legacy?.full_text)
    // }
    // if (isEmpty(mediaWidthMeta)) {
    //   return await ctx.reply(legacy?.full_text ?? '-')
    // }
    // cachedMedia = mediaWidthMeta
    // // 首次要更新 mediaGroups 记录文件大小
    // await updateTweetDetailByTweetId(tweetId, {
    //   mediaGroups: mediaWidthMeta,
    // })

    // // todo 不得大于 Video:20MB Document: 50MB
    // // const baseSize = 20 * 1024 * 1024

    // const mediaGroups = mediaWidthMeta.map(item => buildMediaGroup(item))
    // const msg = await ctx.replyWithMediaGroup(mediaGroups)
    // // 处理回复后的 fileid
    // const sendedMediaGroups = msg.map((item, idx) => {
    //   const fileId = getFileIdFromMessage(item)
    //   // mediaWidthMeta 主要是记录Video 的 size
    //   return fileId ? { ...mediaWidthMeta[idx], fileId } : null
    // }).filter(Boolean)

    // await updateTweetDetailByTweetId(tweetId, {
    //   mediaGroups: sendedMediaGroups as any,
    // })
    // // ! 更新使用额度
    // await updateSessionQuota(ctx, {
    //   isSingleDownload: !cb,
    // })
  }
  catch (error: any) {
    // 如果TG 没解析到视频就主动发送
    // todo !
    // if (overSizeError.some(text => error?.description?.includes(text))) {
    //   // if (cb) {
    //   //   // todo 大文件下载不稳定，需要手动下载
    //   //   return await ctx.reply(`Tweet ID:${tweetId!}
    //   //     \nThe download of large files is currently unstable; manual download is required.`, {
    //   //     reply_markup: await createManualDownloadKeyboard(ctx, { tweetId: tweetId! }),
    //   //   })
    //   // }
    //   if (!membershipStatus.hasQuota) {
    //     return await ctx.reply(ctx.t('membership-quota-exhausted'), {
    //       reply_markup: await createMiniAppKeyboard(ctx, url),
    //     })
    //   }
    //   // todo 超过 2GB 小程序下载
    //   if (cachedMedia?.some(item => item.size > twoGB)) {
    //     // 超过 2GB 手动下载
    //     let replyText = fmt`${bold(ctx.t('download-oversize-reply'))}`
    //     cachedMedia?.forEach((item) => {
    //       replyText = fmt`${bold(replyText)}\n\n${pre(item.url, `${item.type} Link`)}`
    //     })
    //     // const webLink = `https://tgx.one?url=${url}`
    //     return await ctx.replyFmt(replyText, {
    //       reply_markup: await createMiniAppKeyboard(ctx, url),
    //     })
    //   }
    //   // cb?.(tweetId!)
    //   return addDownloadTask(ctx, { tweetId: tweetId!, cachedMedia: cachedMedia! })
    // }
    // if (error?.description?.includes('failed to send message #')) {
    //   // 也可能出tg解析链接失败的情况， 直接用流式下载
    //   try {
    //     const mediaGroups = cachedMedia!.map(item => buildMediaGroup(item, true))
    //     const msg = await ctx.replyWithMediaGroup(mediaGroups)
    //     // 处理回复后的 fileid
    //     const sendedMediaGroups = msg.map((item, idx) => {
    //       const fileId = getFileIdFromMessage(item)
    //       // mediaWidthMeta 主要是记录Video 的 size
    //       return fileId ? { ...cachedMedia![idx], fileId } : null
    //     }).filter(Boolean)

    //     await updateTweetDetailByTweetId(tweetId!, {
    //       mediaGroups: sendedMediaGroups as any,
    //     })
    //   }
    //   catch (error: any) {
    //     logger.error(`tg解析链接失败, 又流式发送错误:${url}\n${error.message}`)
    //     await ctx.reply(ctx.t('error'))
    //   }
    //   return null
    // }

    logger.error(`解析错误:${url}\n${error.message}`)
    if (error.message.includes('prisma')) {
      return await ctx.reply(ctx.t('error'))
    }
    return await ctx.reply(error.message)
  }
  finally {
    ctx.chatAction = null
  }
}

// 批量下载
feature.filter(ctx => ctx.chat?.type === 'private').on('message::url', logHandle('download-message'), async (ctx, next) => {
  const url = extractUrlFromText(ctx.msg.text || ctx.msg.caption)
  if (!url) {
    return await ctx.reply('download-invalid-twitter-url')
  }
  const { isTwitterUrl, isTweeterUser, screenName } = await validateTwitter(url)
  if (!isTwitterUrl) {
    return await ctx.reply(ctx.t('download-invalid-twitter-url'))
  }
  if (!isTweeterUser) {
    return next()
  }
  if (isTweeterUser) {
    logger.info(`批量下载:${screenName}`)
    return await handleBatchDownloadPage(ctx, { screenName })
    // return await ctx.reply(ctx.t('feature-updating'))
  }
})

// 下载分页 按钮点击执行
feature.callbackQuery(
  downloadPageData.filter(),
  logHandle('keyboard-download-page'),
  async (ctx) => {
    const { userTweetsId, btnName } = downloadPageData.unpack(
      ctx.callbackQuery.data,
    )
    const res = await getUserTweetsById(userTweetsId)
    if (!res) {
      return await ctx.answerCallbackQuery({
        text: ctx.t('button-expired'),
        show_alert: true,
      })
    }
    const { topCursorId, bottomCursorId } = res.result as UserTweetsType
    const cursor = btnName === 'top' ? topCursorId : bottomCursorId
    await handleBatchDownloadPage(ctx, { userId: res?.userId, cursor, btnName })
  },
)
// 批量下载 按钮点击执行
const batchDownloadProcessingStatus: Record<string, boolean> = {}
feature.callbackQuery(
  downloadData.filter(),
  logHandle('keyboard-batch-download'),
  async (ctx) => {
    const membershipStatus = validateMembership(ctx)
    // 非会员 验证批量下载是否用完
    if (!membershipStatus.isMembership) {
      if (!membershipStatus.hasTwitterBatchDownloads) {
        return await ctx.answerCallbackQuery({
          text: ctx.t('membership-batchdownload-exhausted'),
          show_alert: true,
        })
      }
    }
    const { userTweetsId } = downloadData.unpack(
      ctx.callbackQuery.data,
    )
    const res = await getUserTweetsById(userTweetsId)
    if (!res) {
      ctx.deleteMessage().catch(silentFn)
      return await ctx.answerCallbackQuery({
        text: ctx.t('button-expired'),
        show_alert: true,
      })
    }
    logger.info(`🔃 点击批量下载推文 ${userTweetsId}`)
    if (batchDownloadProcessingStatus[ctx.session.user!.telegramId]) {
      return await ctx.answerCallbackQuery({
        text: ctx.t('download-batch-processing'),
        show_alert: true,
      })
    }
    batchDownloadProcessingStatus[ctx.session.user!.telegramId] = true
    // 更新批量下载额度
    await updateSessionQuota(ctx, { isBatchDownload: true })
    const { tweets } = res.result as UserTweetsType
    const bigFileTweets = []
    try {
      for (const [index, tweet] of tweets.entries()) {
        const url = `https://x.com/i/status/${tweet.legacy.id_str}`
        await handleSingleDownload(ctx, url, (tId) => {
          bigFileTweets.push(tId)
        })
        // 判断是否是最后一个
        const isLast = index === tweets.length - 1
        if (isLast) {
          if (bigFileTweets.length) {
            ctx.reply(`${ctx.t('download-batch-finished')}\n${ctx.t('download-batch-has-bigfile', {
              num: bigFileTweets.length,
            })}`)
          }
          await ctx.reply(ctx.t('download-batch-finished'))
        }
        // 每发送一条消息后等待 2 秒
        await new Promise(resolve => setTimeout(resolve, 4000))
      }
    }
    catch (error: any) {
      logger.error(`推特批量下载有报错:${error.message}`)
    }
    finally {
      delete batchDownloadProcessingStatus[ctx.session.user!.telegramId]
    }
  },
)

// 手动下载
feature.callbackQuery(
  manualDownloadData.filter(),
  logHandle('keyboard-manual-download'),
  async (ctx) => {
    const { tweetId } = manualDownloadData.unpack(
      ctx.callbackQuery.data,
    )
    const url = `https://x.com/i/status/${tweetId}`
    await handleSingleDownload(ctx, url)
  },
)

// 单个下载
feature.filter(ctx => ctx.chat?.type === 'private').on('message::url', logHandle('download-message'), async (ctx) => {
  // ctx.chatAction = 'typing'
  const url = extractUrlFromText(ctx.msg.text || ctx.msg.caption)
  if (!url) {
    return await ctx.reply('download-invalid-twitter-url')
  }
  const { isTwitterUrl, isTweeterUser } = await validateTwitter(url)
  if (!isTwitterUrl) {
    return await ctx.reply(ctx.t('download-invalid-twitter-url'))
  }

  if (isTweeterUser) {
    return await ctx.reply(ctx.t('feature-updating'))
  }

  // const pendingMsg = await ctx.reply('loading...', {
  //   disable_notification: true,
  // })
  await handleSingleDownload(ctx, url)
})

feature.filter(ctx => ctx.chat?.type === 'private').on('message:text', async (ctx) => {
  return await ctx.reply(ctx.t('download-invalid-text-url'))
})

async function buildMediaWithMeta(media: any) {
  // 格式化 视频信息宽高、获取大小
  const videosWidthMetaPromises = media.map(async (item: any) => {
    if (item.type === 'photo') {
      return Promise.resolve({
        type: 'photo',
        url: item.media_url_https,
        preview: item.media_url_https,
      })
    }
    if (item.type === 'animated_gif') {
      return Promise.resolve({
        type: 'gif',
        url: item.video_info.variants[0].url,
        preview: item.media_url_https,
      })
    }
    if (item.type === 'video') {
      const video = maxBy(item.video_info.variants, 'bitrate') as any
      if (!video)
        return null
      const videoItem = {
        type: 'video',
        url: video.url.split('?')[0],
        preview: item.media_url_https,
        size: null,
        duration: round(item.video_info.duration_millis / 1000),
        width: item.original_info.width,
        height: item.original_info.height,
      }
      return await fetch(video.url, {
        method: 'HEAD',
      }).then((res) => {
        const size = res.headers.get('content-length')
        videoItem.size = size ? Number(size) : size as any
        return videoItem
      }).catch(() => videoItem)
    }
    return undefined
  }).filter(Boolean)
  return await Promise.all(videosWidthMetaPromises!)
}
function buildMediaGroup(item: any, isUseStream?: boolean) {
  const makeStream = (url: string, limit = 2) => {
    return got.stream(url, {
      http2: false, // 开启 HTTP/2
      retry: {
        limit,
      },
    })
  }
  const common = {
    media: item.url,
    caption: item?.caption,
  }
  if (item.type === 'photo') {
    return {
      type: 'photo',
      ...common,
    } as InputMediaPhoto
  }
  if (item.type === 'gif') {
    return {
      type: 'video',
      supports_streaming: true,
      ...common,
    } as InputMediaVideo
  }

  // const isOverSize = item.size > 20 * 1024 * 1024
  if (isUseStream) {
    // 超过 20MB 使用流式发送
    const stream = makeStream(item.url)
    // stream.on('downloadProgress', (p: { percent: number, transferred: number, total: number }) => {
    //   console.log('[ p ] >', idx, p)
    // })
    return InputMediaBuilder.video(
      new InputFile(stream),
      // new InputFile({ url: item.url }),
      {
        width: item.width,
        height: item.height,
        duration: item.duration,
        thumbnail: new InputFile({ url: item.preview }),
        supports_streaming: true,
        caption: item?.caption,
      },
    )
  }
  return {
    type: 'video',
    ...common,
  } as InputMediaVideo
};
function getFileIdFromMessage(item: any): string | null {
  if ('animation' in item) {
    return item.animation?.file_id
  }
  if ('video' in item) {
    return item.video?.file_id
  }
  if ('photo' in item) {
    const photo = maxBy(item.photo, 'file_size') as any
    return photo?.file_id
  }
  return null
}

/**
 * 检查 membership 额度是否已经用完
 */
function validateMembership(ctx: Context) {
  const membership = ctx.session.membership!
  const isMembership = isMembershipActive(membership.expiresAt)
  const hasQuota = membership.effectiveQuota > 0 || membership.extraQuota > 0
  const hasTwitterDownloads = membership.twitterDownloadsLeft > 0
  const hasTwitterBatchDownloads = membership.twitterBatchDownloadsLeft > 0
  return {
    membership,
    isMembership,
    hasQuota,
    hasTwitterDownloads,
    hasTwitterBatchDownloads,
  }
}

/**
 * 更新使用额度
 */
async function updateSessionQuota(
  ctx: Context,
  payload?: {
    effectiveQuota?: bigint
    extraQuota?: bigint
    isSingleDownload?: boolean
    isBatchDownload?: boolean
  },
) {
  try {
    const membership = ctx.session.membership
    if (!membership)
      return null
    // 先更新下载次数
    const twitterDownloadsLeft = membership.twitterDownloadsLeft - (payload?.isSingleDownload ? 1 : 0)
    const twitterBatchDownloadsLeft = membership.twitterBatchDownloadsLeft - (payload?.isBatchDownload ? 1 : 0)
    ctx.session.membership!.twitterDownloadsLeft = twitterDownloadsLeft
    ctx.session.membership!.twitterBatchDownloadsLeft = twitterBatchDownloadsLeft
    await updateMembershipByTelegramId(String(ctx.from!.id), {
      twitterDownloadsLeft,
      twitterBatchDownloadsLeft,
    })
    // 有大文件下载就更新大文件用量额度
    if (payload?.effectiveQuota) {
      ctx.session.membership!.effectiveQuota = payload.effectiveQuota
    }
    if (payload?.extraQuota) {
      ctx.session.membership!.extraQuota = payload.extraQuota
    }
  }
  catch (error) {
    logger.error(`【Session】更新使用额度失败:${error}`)
  }
}

async function addDownloadTask(ctx: Context, payload: { tweetId: string, cachedMedia: any[] }) {
  const { cachedMedia, tweetId } = payload
  const pendingMsg = await ctx.reply('⌛️⌛️⌛️...')
  try {
    const payload = {
      mediaGroups: cachedMedia as any,
      tweetId,
      // 下载完发送的目标，下载完后发送消息只需要chatId就够了, telegramId是为了用户查询自己当前下载任务
      target: {
        telegramId: String(ctx.from!.id),
        chatId: String(ctx.chatId!),
        // 用户发送链接的消息 引用需要用到
        msgId: String(ctx.msgId!),
        // 等待消息的 id
        pendingMsgId: String(pendingMsg.message_id),
      },
    }
    const res = await addTask(payload)
    if (!res.success) {
      pendingMsg.delete().catch(silentFn)
      // todo 失败给用户提示 并 3 秒后删除
      return await ctx.reply(res?.message ?? 'error').then((resMsg) => {
        setTimeout(() => {
          resMsg.delete().catch(silentFn)
        }, 4000)
      })
    }
    else {
      pendingMsg.editText(ctx.t('download-task-processing')).catch(silentFn)
    }
  }
  catch (error: any) {
    pendingMsg.delete().catch(silentFn)
    return ctx.reply(error.message).catch(silentFn)
  }
}

/**
 * 根据质量筛选媒体
 * @param media 原始媒体数组
 * @param quality 质量等级
 */
function filterMediaByQuality(media: Media[], quality: 'low' | 'medium' | 'high', caption: string = '') {
  return media.map((item, idx) => {
    // 处理图片类型
    if (item.type === 'photo') {
      return {
        type: 'photo',
        url: item.mediaUrlHttps,
        size: 0, // 图片大小通常很小
        caption,
      }
    }

    // 处理 GIF 类型
    if (item.type === 'animated_gif') {
      const variants = item.videoInfo?.variants
      if (!variants || variants.length === 0)
        return null

      return {
        type: 'gif',
        url: variants[0].url,
        size: 0, // GIF 大小
        caption,
      }
    }

    // 处理视频类型
    if (item.type === 'video') {
      const videoInfo = item.videoInfo
      if (!videoInfo?.variants)
        return null

      const variants = videoInfo.variants.filter(v =>
        v.contentType === 'video/mp4',
      )

      if (variants.length === 0)
        return null

      // 根据质量选择对应的视频
      let selectedVariant
      if (quality === 'low') {
        // 选择最低码率
        selectedVariant = minBy(variants.filter((v: any) => v.bitrate > 0), 'bitrate') || variants[0]
      }
      else if (quality === 'medium') {
        // 选择中等码率
        const sortedVariants = variants.filter((v: any) => v.bitrate > 0)
        if (sortedVariants.length === 0) {
          selectedVariant = variants[0]
        }
        else {
          const middleIndex = Math.floor(sortedVariants.length / 2)
          selectedVariant = sortBy(sortedVariants, 'bitrate')[middleIndex]
        }
      }
      else {
        // high - 选择最高码率
        selectedVariant = maxBy(variants, 'bitrate') || variants[0]
      }

      if (!selectedVariant)
        return null

      // 根据码率和时长计算文件大小 (bitrate * duration / 8)
      // bitrate 单位是 bps (bits per second)，duration 单位是毫秒
      const bitrate = selectedVariant.bitrate || 0
      const durationMs = videoInfo.durationMillis || 0
      const estimatedSize = bitrate > 0 && durationMs > 0
        ? Math.round((bitrate * durationMs) / 8 / 1000) // 转换为字节
        : 0

      return {
        type: 'video',
        url: selectedVariant.url.split('?')[0],
        size: estimatedSize,
        caption,
      }
    }

    return null
  }).filter(Boolean)
}

export { composer as downloadFeature }
