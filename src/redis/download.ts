import { Queue } from 'bullmq'
import type { InputMedia } from 'grammy/types'
import { redisConfig } from './client.js'

// !下载任务 定义两个服务保持一致
interface Target {
  msgId: string
  // 等待消息的 id
  pendingMsgId: string
  telegramId: string
  chatId: string
}
interface DownloadJobData {
  mediaGroups: InputMedia & { size: number } []
  tweetId: string
  // 用户发送链接的消息 引用需要用到
  targetList: Target[]
}

interface AddDownloadPayload {
  mediaGroups: InputMedia & { size: number } []
  tweetId: string
  target: Target
}

const downloadQueueName = 'downloadQueue'
export const downloadQueue = new Queue<DownloadJobData>(downloadQueueName, {
  connection: redisConfig,
  defaultJobOptions: {
    attempts: 0, // 每个任务最多尝试 2 次
    backoff: {
      type: 'fixed', // 重试间隔为固定时间
      delay: 2000, // 每次重试间隔 2000 毫秒（2 秒）
    },
  },
})

export async function addTask(payload: AddDownloadPayload) {
  const { tweetId, mediaGroups, target } = payload
  try {
    // 检查是否已有该任务
    const existingJob = await downloadQueue.getJob(tweetId)
    const jobState = await existingJob?.getState()

    if (jobState === 'failed') {
      await existingJob?.remove()
    }
    const jobs = await downloadQueue.getJobs(['waiting', 'active'])
    if (existingJob && (jobState === 'active' || jobState === 'waiting')) {
      // 任务已存在，更新目标用户列表
      const existingUsers = existingJob.data.targetList || []
      if (!existingUsers.some(item => item.chatId === target.chatId && item.telegramId === target.telegramId)) {
        existingUsers.push(target)
        await existingJob.updateData({ ...existingJob.data, targetList: existingUsers })
      }
      const idx = jobs.findIndex(item => item.id === existingJob.id)
      return { success: true, message: 'Task already in queue', tweetId, idx: idx + 1 }
    }

    // 添加新任务到队列
    const job = await downloadQueue.add(
      tweetId,
      { tweetId, mediaGroups, targetList: [target] },
      { jobId: tweetId, removeOnFail: true },
    )
    const idx = jobs.findIndex(item => item.id === job.id)

    return { success: true, message: 'Task added', tweetId, idx: idx + 1 }
  }
  catch (error) {
    return { success: false, message: 'Task error', error }
  }
}

export async function getTaskByTelegramId(telegramId: string) {
  const jobs = await downloadQueue.getJobs(['waiting', 'active'])
  const userTasks = jobs
    .filter(job => job.data.targetList.some(item => +item.telegramId === +telegramId))
    .map((job) => {
      // 计算该任务的总文件大小
      const totalSize = job.data.mediaGroups.reduce((sum, media) => sum + (+media.size || 0), 0)
      return {
        id: job.id,
        ...job.data,
        totalSize, // 添加总大小字段
      }
    })
    // 计算所有任务的总大小
  const totalTasksSize = userTasks.reduce((sum, task) => sum + task.totalSize, 0)
  return {
    tasks: userTasks,
    totalTasksSize,
    count: userTasks.length,
  }
}
export async function getAllTask() {
  const jobs = await downloadQueue.getJobs(['waiting', 'active'])
  const userTasks = jobs.map((job) => {
    // 计算该任务的总文件大小
    const totalSize = job.data.mediaGroups.reduce((sum, media) => sum + (+media.size || 0), 0)
    return {
      id: job.id,
      ...job.data,
      totalSize, // 添加总大小字段
    }
  })
  // 计算所有任务的总大小
  const totalTasksSize = userTasks.reduce((sum, task) => sum + task.totalSize, 0)
  return {
    tasks: userTasks,
    totalTasksSize,
    count: userTasks.length,
  }
}
