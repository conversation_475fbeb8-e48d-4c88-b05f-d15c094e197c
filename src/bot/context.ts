import type { Update, UserFromGetMe } from '@grammyjs/types'
import { type Api, Context as DefaultContext, type SessionFlavor } from 'grammy'
import type { AutoChatActionFlavor } from '@grammyjs/auto-chat-action'
import type { HydrateFlavor } from '@grammyjs/hydrate'
import type { I18nFlavor } from '@grammyjs/i18n'
import type { ParseModeFlavor } from '@grammyjs/parse-mode'
import type { Conversation, ConversationFlavor } from '@grammyjs/conversations'

import type { Membership, Telegram } from '@prisma/client'
import type { FileFlavor } from '@grammyjs/files'
import type { Logger } from '#root/logger.js'
import type { Config } from '#root/config.js'

export interface SessionData {
  __language_code?: string
  user?: Telegram
  membership?: Membership
  // 购买按钮状态，防止用户重复点击
  isPurchaseProcessing?: boolean
  //  session更新时间 用来刷新membership 等
  updateAt?: Date
  // 批量下载处理状态
  isBatchDownloadProcessing?: boolean
}

interface ExtendedContextFlavor {
  logger: Logger
  config: Config
}

export type Context = ConversationFlavor<
  FileFlavor<
    ParseModeFlavor<
      HydrateFlavor<
        DefaultContext &
        ExtendedContextFlavor &
        SessionFlavor<SessionData> &
        I18nFlavor &
        AutoChatActionFlavor
      >
    >
  >
>

// 内部上下文对象 (知道所有对话插件)
export type MyConversationContext = DefaultContext
export type MyConversation = Conversation<Context, MyConversationContext>

interface Dependencies {
  logger: Logger
  config: Config
}

export function createContextConstructor(
  {
    logger,
    config,
  }: Dependencies,
) {
  return class extends DefaultContext implements ExtendedContextFlavor {
    logger: Logger
    config: Config

    constructor(update: Update, api: Api, me: UserFromGetMe) {
      super(update, api, me)

      this.logger = logger.child({
        update_id: this.update.update_id,
      })
      this.config = config
    }
  } as unknown as new (update: Update, api: Api, me: UserFromGetMe) => Context
}
