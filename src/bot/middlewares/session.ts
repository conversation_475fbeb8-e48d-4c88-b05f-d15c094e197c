import type { Enhance, Middleware, SessionOptions } from 'grammy'
import { session as createSession, enhanceStorage } from 'grammy'
import { PrismaAdapter } from '@grammyjs/storage-prisma'
import type { Context, SessionData } from '#root/bot/context.js'
import prisma from '#root/db/index.js'

type Options = Pick<SessionOptions<SessionData, Context>, 'getSessionKey' | 'storage'>

export function session(options: Options): Middleware<Context> {
  const storage = new PrismaAdapter<Enhance<SessionData>>(prisma.session)
  return createSession({
    getSessionKey: options.getSessionKey,
    storage: enhanceStorage({
      storage,
      // 一小时过期
      millisecondsToLive: 60 * 60 * 1000,
    }),
    initial: () => ({}),
  })
}
